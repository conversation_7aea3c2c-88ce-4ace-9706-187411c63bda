import { computed, ref } from 'vue'
import { wktToMapComponents } from '@/utils/wktConverter'
import { useTaskStore } from '@/store'

export function useMapData() {
  const taskStore = useTaskStore()

  // ==================== 地图基础状态 ====================
  const latitude = ref(42.881636)
  const longitude = ref(129.382471)
  const centerMarker = ref({ latitude: 42.881636, longitude: 129.382471 })
  const scale = ref(14)
  const isMapMoving = ref(false)
  const isShowCenterMarker = ref(true)

  // ==================== 地图数据 ====================
  const initPoints = ref([])
  const initPolygons = ref([])
  const initPolylines = ref([])

  // ==================== 地图计算属性 ====================
  const covers = computed(() => {
    return [...initPoints.value]
  })

  const allPolygons = computed(() => {
    return [...initPolygons.value]
  })

  const allPolylines = computed(() => {
    return [...initPolylines.value]
  })

  // ==================== 地图数据处理方法 ====================
  /**
   * 根据房屋状态获取多边形颜色配置
   */
  const getPolygonColorByStatus = (status: string, isSelected = false) => {
    if (isSelected) {
      return {
        fillColor: '#409eff33', // 使用选中面颜色的透明版本
        strokeColor: '#409eff', // 选中面颜色
        strokeWidth: 3,
      }
    }

    const colorMap = {
      completed: {
        fillColor: '#67c23a33', // 已完成状态颜色的透明版本
        strokeColor: '#67c23a', // 已完成状态颜色
        strokeWidth: 2,
      },
      in_progress: {
        fillColor: '#e6a23c33', // 进行中状态颜色的透明版本
        strokeColor: '#e6a23c', // 进行中状态颜色
        strokeWidth: 2,
      },
      default: {
        fillColor: '#f56c6c33', // 未开始状态颜色的透明版本
        strokeColor: '#f56c6c', // 未开始状态颜色
        strokeWidth: 2,
      },
    }

    return colorMap[status] || colorMap.default
  }

  /**
   * 将WKT转换结果添加到地图
   */
  const addWktDataToMap = (
    wktResult: any,
    styles: any,
    dataSource: string,
    house?: any,
    skipLabel = false,
    taskType?: string,
  ) => {
    if (
      !wktResult ||
      (!wktResult.markers?.length && !wktResult.polylines?.length && !wktResult.polygons?.length)
    ) {
      return
    }

    // 添加标记点
    if (wktResult.markers?.length > 0) {
      wktResult.markers.forEach((marker: any, index: number) => {
        const mapMarker = {
          id: `${dataSource}_marker_${initPoints.value.length + index + 1}`,
          latitude: marker.latitude,
          longitude: marker.longitude,
          iconPath: styles.marker.iconPath,
          width: styles.marker.width,
          height: styles.marker.height,
          dataSource,
          house,
        }
        initPoints.value.push(mapMarker)
      })
    }

    // 添加多边形
    if (wktResult.polygons?.length > 0) {
      wktResult.polygons.forEach((polygon: any, index: number) => {
        const mapPolygon = {
          points: polygon.points,
          fillColor: styles.polygon.fillColor,
          strokeColor: styles.polygon.strokeColor,
          strokeWidth: styles.polygon.strokeWidth,
          dataSource,
          id: `${dataSource}_polygon_${index}`,
          house,
        }
        initPolygons.value.push(mapPolygon)

        // 为多边形添加标签（如果不跳过标签）
        if (!skipLabel) {
          let labelText = ''
          if (house) {
            // 根据任务类型决定显示内容
            if (taskType === 'ZF') {
              // 住房类型：显示房屋id、房屋名称或地址
              labelText = house.fwjzdm || house.houseName || house.address || `区域 ${index + 1}`
            } else {
              // 区域类型（小区、社区、街区等）：优先显示name字段
              labelText =
                house.name ||
                house.fwjzdm ||
                house.houseName ||
                house.address ||
                `区域 ${index + 1}`
            }
          } else {
            // 其他情况显示name，如果没有name则显示默认标签
            labelText = polygon.name || `区域 ${index + 1}`
          }

          addPolygonLabel(polygon, labelText, dataSource, index, house)
        }
      })
    }

    // 添加线段
    if (wktResult.polylines?.length > 0) {
      wktResult.polylines.forEach((polyline: any, index: number) => {
        const mapPolyline = {
          points: polyline.points,
          width: styles.polyline.width,
          color: styles.polyline.color,
          dataSource,
          id: `${dataSource}_polyline_${index}`,
          house,
        }
        initPolylines.value.push(mapPolyline)
      })
    }

    // 调整地图视野
    adjustMapView(wktResult)
  }

  /**
   * 调整地图视野以适应所有几何图形
   */
  const adjustMapView = (wktResult: any) => {
    const allPoints = []

    // 收集所有坐标点
    wktResult.markers?.forEach((marker: any) => {
      allPoints.push({ latitude: marker.latitude, longitude: marker.longitude })
    })

    wktResult.polylines?.forEach((polyline: any) => {
      allPoints.push(...polyline.points)
    })

    wktResult.polygons?.forEach((polygon: any) => {
      allPoints.push(...polygon.points)
    })

    // 如果有坐标点，调整地图中心和缩放级别
    if (allPoints.length > 0) {
      const avgLat = allPoints.reduce((sum, p) => sum + p.latitude, 0) / allPoints.length
      const avgLon = allPoints.reduce((sum, p) => sum + p.longitude, 0) / allPoints.length

      // 只在第一次有数据时调整地图中心
      if (latitude.value === 42.881636 && longitude.value === 129.382471) {
        latitude.value = avgLat
        longitude.value = avgLon
        centerMarker.value = { latitude: avgLat, longitude: avgLon }
      }
    }
  }

  /**
   * 处理全局变量WKT数据
   */
  const processGlobalWktData = () => {
    const currentWktGeom = taskStore.getTaskWktGeom()
    if (!currentWktGeom) return

    // 获取当前任务状态
    const currentTaskStatus = taskStore.getTaskStatus()
    console.log('🗺️ 处理全局WKT数据，任务状态:', currentTaskStatus)

    try {
      const result = wktToMapComponents(currentWktGeom)

      // 根据任务状态获取颜色配置
      const globalPolygonColors = getPolygonColorByStatus(getStatusString(currentTaskStatus), false)

      addWktDataToMap(
        result,
        {
          marker: {
            iconPath: '/static/map/icon/marker.png',
            width: 32,
            height: 32,
          },
          polygon: {
            fillColor: globalPolygonColors.fillColor,
            strokeColor: globalPolygonColors.strokeColor,
            strokeWidth: globalPolygonColors.strokeWidth,
          },
          polyline: {
            width: 4,
            color: globalPolygonColors.strokeColor,
          },
        },
        'global',
        null,
      )
    } catch (error) {
      console.error('❌ 全局WKT转换失败:', error)
      uni.showToast({
        title: '全局WKT数据转换失败',
        icon: 'error',
      })
    }
  }

  /**
   * 将数字状态转换为字符串状态
   * @param status 状态（数字或字符串）
   * @returns 状态字符串
   */
  const getStatusString = (status: number | string): string => {
    if (typeof status === 'string') {
      return status
    }

    // 数字状态转换为字符串状态
    const statusMap = {
      0: 'default', // 未开始
      1: 'in_progress', // 进行中
      2: 'completed', // 已完成
    }

    return statusMap[status as number] || 'default'
  }

  /**
   * 计算多边形的中心点（质心）
   * @param points 多边形的坐标点数组
   * @returns 中心点坐标
   */
  const calculatePolygonCenter = (points: any[]): { latitude: number; longitude: number } => {
    if (!points || points.length === 0) {
      return { latitude: 0, longitude: 0 }
    }

    if (points.length === 1) {
      return { latitude: points[0].latitude, longitude: points[0].longitude }
    }

    // 计算几何中心点（简单平均）
    let totalLat = 0
    let totalLng = 0
    let validPointsCount = 0

    points.forEach((point) => {
      if (point && typeof point.latitude === 'number' && typeof point.longitude === 'number') {
        totalLat += point.latitude
        totalLng += point.longitude
        validPointsCount++
      }
    })

    if (validPointsCount === 0) {
      return { latitude: 0, longitude: 0 }
    }

    return {
      latitude: totalLat / validPointsCount,
      longitude: totalLng / validPointsCount,
    }
  }

  /**
   * 为多边形添加中心标签
   * @param polygon 多边形对象
   * @param labelText 标签文本
   * @param dataSource 数据源标识
   * @param index 多边形索引
   * @param house 房屋数据（可选）
   */
  const addPolygonLabel = (
    polygon: any,
    labelText: string,
    dataSource: string,
    index: number,
    house?: any,
  ) => {
    if (!polygon || !polygon.points || polygon.points.length < 3) {
      return
    }

    const center = calculatePolygonCenter(polygon.points)

    // 创建标签marker
    const labelMarker = {
      id: `${dataSource}_polygon_label_${index}`,
      latitude: center.latitude,
      longitude: center.longitude,
      iconPath: '/static/map/icon/marker.png', // 使用现有图标
      width: 1,
      height: 1,
      alpha: 0, // 完全透明
      label: {
        content: labelText,
        color: '#ffffff', // 白色字体
        fontSize: 14,
        borderRadius: 0, // 去掉圆角
        bgColor: 'transparent', // 透明背景
        padding: 0, // 去掉内边距
        borderWidth: 0, // 去掉边框
        borderColor: 'transparent', // 透明边框
        anchorX: 0,
        anchorY: 0,
        display: 'ALWAYS', // 始终显示标签，不需要点击
      },
      dataSource,
      house,
      isPolygonLabel: true, // 标记这是多边形标签
    }

    initPoints.value.push(labelMarker)
    console.log(`✅ 添加多边形标签: ${labelText}`, labelMarker)
  }

  /**
   * 处理房屋数据
   */
  const processHouseData = (responseData: any[], taskType?: string) => {
    if (!responseData?.length) return

    responseData.forEach((house, index) => {
      if (house.geom && typeof house.geom === 'string') {
        try {
          const result = wktToMapComponents(house.geom)
          // 🚀 修复：先转换状态格式，再获取颜色配置
          const statusString = getStatusString(house.status)
          const polygonColors = getPolygonColorByStatus(statusString, false)

          addWktDataToMap(
            result,
            {
              marker: {
                iconPath: '/static/map/icon/marker.png',
                width: 28,
                height: 28,
              },
              polygon: polygonColors,
              polyline: {
                width: 3,
                color: polygonColors.strokeColor,
              },
            },
            `house_${index}`,
            house,
            false, // 不跳过标签
            taskType, // 传递任务类型
          )
        } catch (error) {
          console.error(`❌ 房屋 ${index + 1} WKT转换失败:`, error)
        }
      }
    })
  }

  /**
   * 处理位置查询的房屋数据
   */
  const processLocationHouseData = (locationHouseData: any[], taskType?: string) => {
    if (!locationHouseData?.length) return

    // 先保存当前所有的多边形标签
    const existingLabels = initPoints.value.filter((item) => item.isPolygonLabel === true)

    locationHouseData.forEach((house, index) => {
      if (house.geom && typeof house.geom === 'string') {
        try {
          const result = wktToMapComponents(house.geom)

          // 添加位置查询数据，但不添加新标签
          const skipLabel = true // 跳过添加新标签

          addWktDataToMap(
            result,
            {
              marker: {
                iconPath: '/static/map/icon/marker.png',
                width: 30,
                height: 30,
              },
              polygon: {
                fillColor: '#409eff66', // 选中面颜色的透明版本，用于位置查询高亮
                strokeColor: '#409eff', // 选中面颜色，用于位置查询高亮
                strokeWidth: 4,
              },
              polyline: {
                width: 4,
                color: '#409eff', // 选中面颜色，用于位置查询高亮
              },
            },
            `location_house_${index}`,
            house,
            skipLabel, // 传递跳过标签的标志
            taskType, // 传递任务类型
          )
        } catch (error) {
          console.error(`❌ 位置房屋 ${index + 1} WKT转换失败:`, error)
        }
      }
    })

    // 确保所有原有标签都被保留
    existingLabels.forEach((label) => {
      if (!initPoints.value.some((item) => item.id === label.id)) {
        initPoints.value.push(label)
      }
    })
  }

  /**
   * 调整地图视野到房屋位置
   */
  const adjustMapViewToHouse = (house: any) => {
    if (!house.geom) return

    try {
      const result = wktToMapComponents(house.geom)

      if (result.markers && result.markers.length > 0) {
        const marker = result.markers[0]
        latitude.value = marker.latitude
        longitude.value = marker.longitude
        centerMarker.value = { latitude: marker.latitude, longitude: marker.longitude }
        scale.value = 16 // 放大地图
      } else if (result.polygons && result.polygons.length > 0) {
        const polygon = result.polygons[0]
        if (polygon.points && polygon.points.length > 0) {
          // 计算多边形中心点
          const avgLat =
            polygon.points.reduce((sum, p) => sum + p.latitude, 0) / polygon.points.length
          const avgLon =
            polygon.points.reduce((sum, p) => sum + p.longitude, 0) / polygon.points.length
          latitude.value = avgLat
          longitude.value = avgLon
          centerMarker.value = { latitude: avgLat, longitude: avgLon }

          // 根据多边形大小调整缩放级别
          const latRange =
            Math.max(...polygon.points.map((p) => p.latitude)) -
            Math.min(...polygon.points.map((p) => p.latitude))
          const lonRange =
            Math.max(...polygon.points.map((p) => p.longitude)) -
            Math.min(...polygon.points.map((p) => p.longitude))
          const maxRange = Math.max(latRange, lonRange)

          if (maxRange < 0.001) {
            scale.value = 18
          } else if (maxRange < 0.005) {
            scale.value = 16
          } else if (maxRange < 0.01) {
            scale.value = 14
          } else {
            scale.value = 12
          }
        }
      } else if (result.polylines && result.polylines.length > 0) {
        const polyline = result.polylines[0]
        if (polyline.points && polyline.points.length > 0) {
          const avgLat =
            polyline.points.reduce((sum, p) => sum + p.latitude, 0) / polyline.points.length
          const avgLon =
            polyline.points.reduce((sum, p) => sum + p.longitude, 0) / polyline.points.length
          latitude.value = avgLat
          longitude.value = avgLon
          centerMarker.value = { latitude: avgLat, longitude: avgLon }
          scale.value = 15
        }
      }
    } catch (error) {
      console.error('❌ 调整地图视野失败:', error)
    }
  }

  /**
   * 清除地图数据
   */
  const clearMapData = () => {
    // 清除所有标记点和标签
    initPoints.value = []
    initPolygons.value = []
    initPolylines.value = []
  }

  /**
   * 清除位置查询的房屋数据
   */
  const clearLocationHouseData = () => {
    // 清除位置查询的标记点，但保留所有多边形标签
    initPoints.value = initPoints.value.filter((item: any) => {
      // 如果是多边形标签，始终保留
      if (item.isPolygonLabel === true) {
        return true
      }
      // 如果不是location_house_开头的数据源，保留
      if (!item.dataSource?.startsWith('location_house_')) {
        return true
      }
      // 其他情况（location_house_开头且不是多边形标签）删除
      return false
    })

    initPolygons.value = initPolygons.value.filter(
      (item: any) => !item.dataSource?.startsWith('location_house_'),
    )
    initPolylines.value = initPolylines.value.filter(
      (item: any) => !item.dataSource?.startsWith('location_house_'),
    )
  }

  /**
   * 地图缩放控制
   */
  const zoomIn = () => {
    if (scale.value < 20) {
      scale.value = Math.min(20, scale.value + 1)
      uni.showToast({
        title: `缩放级别: ${scale.value}`,
        icon: 'none',
        duration: 1000,
      })
    } else {
      uni.showToast({
        title: '已达到最大缩放级别',
        icon: 'none',
        duration: 1000,
      })
    }
  }

  const zoomOut = () => {
    if (scale.value > 5) {
      scale.value = Math.max(5, scale.value - 1)
      uni.showToast({
        title: `缩放级别: ${scale.value}`,
        icon: 'none',
        duration: 1000,
      })
    } else {
      uni.showToast({
        title: '已达到最小缩放级别',
        icon: 'none',
        duration: 1000,
      })
    }
  }

  /**
   * 处理地图移动事件
   */
  const handleRegionChange = (e: any) => {
    isMapMoving.value = e.type === 'begin'
  }

  return {
    // 状态
    latitude,
    longitude,
    centerMarker,
    scale,
    isMapMoving,
    isShowCenterMarker,
    covers,
    allPolygons,
    allPolylines,

    // 方法
    processGlobalWktData,
    processHouseData,
    processLocationHouseData,
    adjustMapViewToHouse,
    clearMapData,
    clearLocationHouseData,
    addWktDataToMap,
    getPolygonColorByStatus,
    getStatusString,
    calculatePolygonCenter,
    addPolygonLabel,
    zoomIn,
    zoomOut,
    handleRegionChange,
  }
}
