<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '绘制',
  },
}
</route>
<template>
  <!-- 页面头部 - 使用 wd-navbar 组件 -->
  <wd-navbar
    :title="pageTitle"
    left-arrow
    left-text="返回"
    safe-area-inset-top
    @click-left="goBack"
  >
    <template #right>
      <!--      <wd-button size="small" type="primary" @click="confirmDrawing">确定</wd-button>-->
    </template>
  </wd-navbar>
  <view class="map-draw-page">
    <!-- 地图容器 -->
    <view class="map-container">
      <!-- 消息通知组件 -->
      <wd-notify />
      <!-- 消息对话框组件 -->
      <wd-message-box />

      <map
        id="mapDraw"
        :enable-satellite="true"
        :latitude="mapLatitude"
        :longitude="mapLongitude"
        :markers="mapMarkers"
        :polygons="mapPolygons"
        :polyline="mapPolylines"
        :scale="mapScale"
        :show-location="true"
        class="map-view"
        @regionchange="handleRegionChange"
      >
        <!-- 距离显示面板 - 显示在地图左上角 -->
        <view v-if="distanceInfo.visible" class="distance-panel">
          <view class="distance-text">{{ distanceInfo.displayText }}</view>
        </view>

        <!-- 地图中心点标记 - 始终显示 -->
        <view class="center-marker"></view>
        <!-- 绘制界面 -->
        <view v-if="showDrawControls" class="draw-view">
          <!-- 绘制控制按钮 -->
          <view class="draw-control-buttons simple">
            <view class="draw-btn" @click="confirmPoint">
              <wd-icon name="check" size="22px" />
            </view>
            <view class="draw-btn" @click="cancelPoint">
              <wd-icon name="close" size="22px" />
            </view>
            <view class="draw-btn" @click="clearAllWithConfirm">
              <wd-icon name="delete" size="22px" />
            </view>
            <view
              v-if="currentMode === 'line' || currentMode === 'polygon'"
              class="draw-btn"
              @click="finishDrawing"
            >
              <wd-icon name="check-outline" size="22px" />
            </view>
            <view
              v-if="currentMode === 'point' && hasDrawnContent"
              class="draw-btn"
              @click="confirmDrawing"
            >
              <wd-icon name="check-outline" size="22px"></wd-icon>
            </view>
          </view>
        </view>
      </map>

      <!-- 控制面板 -->
      <!--      <view class="control-panel simple">-->
      <!--        <view-->
      <!--          v-if="isModeAllowed('point')"-->
      <!--          :class="{ active: currentMode === 'point' }"-->
      <!--          class="panel-btn"-->
      <!--          @click="setMode('point')"-->
      <!--        >-->
      <!--          <wd-icon name="location" size="22px" />-->
      <!--        </view>-->
      <!--        <view-->
      <!--          v-if="isModeAllowed('line')"-->
      <!--          :class="{ active: currentMode === 'line' }"-->
      <!--          class="panel-btn"-->
      <!--          @click="setMode('line')"-->
      <!--        >-->
      <!--          <wd-icon name="decrease" size="22px" />-->
      <!--        </view>-->
      <!--        <view-->
      <!--          v-if="isModeAllowed('polygon')"-->
      <!--          :class="{ active: currentMode === 'polygon' }"-->
      <!--          class="panel-btn"-->
      <!--          @click="setMode('polygon')"-->
      <!--        >-->
      <!--          <wd-icon name="rectangle" size="22px" />-->
      <!--        </view>-->
      <!--      </view>-->

      <!-- 状态显示 -->
      <!--      <view v-if="hasDrawnContent" class="status-panel">-->
      <!--        <view class="status-text">{{ statusText }}</view>-->
      <!--        <view class="status-detail">-->
      <!--          <text v-if="drawData.points.length > 0" class="status-item point">-->
      <!--            📍 {{ drawData.points.length }}个点标记-->
      <!--          </text>-->
      <!--          <text v-if="drawData.polylines.length > 0" class="status-item line">-->
      <!--            📏 {{ drawData.polylines.length }}条线段-->
      <!--          </text>-->
      <!--          <text v-if="drawData.polygons.length > 0" class="status-item polygon">-->
      <!--            🔷 {{ drawData.polygons.length }}个多边形-->
      <!--          </text>-->
      <!--        </view>-->
      <!--      </view>-->
      <!-- 绘制提示 -->
      <!--      <view v-if="currentMode" class="draw-hint-panel">-->
      <!--        <view class="hint-text">-->
      <!--          <text v-if="currentMode === 'point'">💡 点击地图中心标记添加点位</text>-->
      <!--          <text v-if="currentMode === 'line'">-->
      <!--            💡 点击地图中心标记添加线段节点 ({{ drawData.currentPoints.length }}个点)-->
      <!--          </text>-->
      <!--          <text v-if="currentMode === 'polygon'">-->
      <!--            💡 点击地图中心标记添加多边形顶点 ({{ drawData.currentPoints.length }}个点)-->
      <!--          </text>-->
      <!--        </view>-->
      <!--      </view>-->
    </view>
  </view>
</template>

<style lang="scss" scoped>
.map-draw-page {
  /* 确保内容不被安全区域遮挡 */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #fff;
}

.map-container {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.map-view {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.draw-view {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  pointer-events: none;
}
/* 距离显示面板样式 - 科技感透明风格 */
.distance-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  padding: 10px 16px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 6px;
  box-shadow:
    0 0 10px rgba(0, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.distance-text {
  font-size: 14px;
  font-weight: 500;
  color: #00ffff;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
  letter-spacing: 0.5px;
}

.center-marker {
  position: absolute;
  top: calc(50% - 20px);
  left: calc(50% - 20px);
  width: 32px;
  height: 32px;
  pointer-events: none;
  background-image: url('@/static/fixedPoint.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  transform: translateZ(0); /* 启用硬件加速 */
}

.draw-control-buttons.simple {
  position: fixed;
  bottom: 80px;
  left: 50%;
  z-index: 1000;
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
  padding: 8px 14px;
  pointer-events: auto;
  background: transparent;
  border-radius: 12px;
  box-shadow: none;
  transform: translateX(-50%);
}

.draw-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 33px;
  height: 33px;
  cursor: pointer;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 50%;
  box-shadow: 0 2px 8px #00000014;
  transition: box-shadow 0.2s;
}

.draw-btn:active {
  box-shadow: 0 0 0 2px #e0e0e0;
}

.control-panel.simple {
  position: absolute;
  top: 20px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 2px;
  background: transparent;
  border-radius: 8px;
  box-shadow: none;
}

.panel-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  cursor: pointer;
  background: #fff;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  box-shadow: 0 1px 4px #0000000a;
  transition:
    background 0.2s,
    box-shadow 0.2s,
    border 0.2s;
}

.panel-btn:active,
.panel-btn:hover {
  background: #f5f5f5;
  box-shadow: 0 2px 8px #00000014;
}

.panel-btn .wd-icon {
  font-size: 22px !important;
}

.panel-btn.active {
  background: #e6f0fa;
  border: 2px solid #2196f3;
}

.panel-btn.active .wd-icon {
  color: #2196f3 !important;
  transition: color 0.2s;
}

.status-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  max-width: 180px;
  padding: 8px 12px;
  background: #ffffffe6;
  border-radius: 6px;
  box-shadow: 0 2px 8px #0000001a;
}

.status-text {
  font-size: 12px;
  color: #666;
}

.status-detail {
  margin-top: 8px;
}

.status-item {
  display: block;
  margin-bottom: 4px;
}

.draw-hint-panel {
  position: absolute;
  right: 20px;
  bottom: 20px;
  max-width: 200px;
  padding: 8px 12px;
  background: #ffffffe6;
  border-radius: 6px;
  box-shadow: 0 2px 8px #0000001a;
}

.hint-text {
  font-size: 12px;
  color: #666;
}
/* 去掉弹出框的黑色遮罩 */
:deep(.wd-overlay) {
  background-color: transparent !important;
}

:deep(.wd-message-box__overlay) {
  background-color: transparent !important;
}

:deep(.wd-popup__overlay) {
  background-color: transparent !important;
}
</style>

<script lang="ts" setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { type CoordPoint, gcj02ToWgs84, wgs84ToGcj02 } from '@/utils/coordTransformNew'
import { useMessage, useNotify } from 'wot-design-uni' // 初始化消息通知和对话框
import { getValueByKey } from '@/service/userAPI'

// 初始化消息通知和对话框
const { showNotify } = useNotify()
const { confirm, alert } = useMessage()

// 页面参数
const pageTitle = ref('地图绘制')
const fieldName = ref('mapSelector')
const allowedModes = ref<string[]>(['Point', 'LineString', 'Polygon']) // 允许的绘制模式
const allowMultiple = ref(false) // 默认只允许选择一个几何图形
const sourceCoordType = ref<string>('') // 输入数据的坐标系类型

// 基础状态
const currentMode = ref('')
const showDrawControls = ref(false)

// 地图状态
const isMapMoving = ref(false)

// 地图配置
const mapLatitude = ref(43.823755)
const mapLongitude = ref(125.277062)
const mapScale = ref(16)

// 绘制数据
let drawData = {
  points: [] as any[],
  polylines: [] as any[],
  polygons: [] as any[],
  currentPoints: [] as any[],
}

// 地图显示数据 - 静态数组
const mapMarkers = ref<any[]>([])
const mapPolygons = ref<any[]>([])
const mapPolylines = ref<any[]>([])

// 计算属性
const hasDrawnContent = ref(false)
const statusText = ref('')

// 距离信息显示
const distanceInfo = ref({
  visible: false,
  displayText: '',
})

// 全局数学常量
const PI = Math.PI
const X_PI = (PI * 3000.0) / 180.0
const A = 6378245.0
// 使用计算方式避免精度问题
const EE = 1 - (6356752.3142 / 6378245.0) * (6356752.3142 / 6378245.0)

// 判断是否在中国境外
const isOutOfChina = (lng: number, lat: number) => {
  return lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271
}

// 计算两点间的距离（使用Haversine公式）
const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
  const R = 6371000 // 地球半径，单位：米
  const dLat = ((lat2 - lat1) * Math.PI) / 180
  const dLng = ((lng2 - lng1) * Math.PI) / 180
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

// 格式化距离显示
const formatDistance = (distance: number): string => {
  if (distance < 1000) {
    return `${distance.toFixed(1)}m`
  } else if (distance < 10000) {
    return `${(distance / 1000).toFixed(2)}km`
  } else {
    return `${(distance / 1000).toFixed(1)}km`
  }
}

// 计算线段的总距离
const calculatePolylineDistance = (points: any[]): number => {
  if (!points || points.length < 2) return 0

  let totalDistance = 0
  for (let i = 0; i < points.length - 1; i++) {
    const point1 = points[i]
    const point2 = points[i + 1]
    if (
      isValidCoordinate(point1.latitude, point1.longitude) &&
      isValidCoordinate(point2.latitude, point2.longitude)
    ) {
      totalDistance += calculateDistance(
        point1.latitude,
        point1.longitude,
        point2.latitude,
        point2.longitude,
      )
    }
  }
  return totalDistance
}

// 验证坐标是否有效
const isValidCoordinate = (lat: number, lng: number): boolean => {
  return (
    typeof lat === 'number' &&
    typeof lng === 'number' &&
    !isNaN(lat) &&
    !isNaN(lng) &&
    lat >= -90 &&
    lat <= 90 &&
    lng >= -180 &&
    lng <= 180
  )
}

// 更新距离显示
const updateDistanceDisplay = () => {
  // 计算所有已完成线段的总距离
  let totalDistance = 0
  drawData.polylines.forEach((polyline: any) => {
    if (polyline.points && polyline.points.length >= 2) {
      totalDistance += calculatePolylineDistance(polyline.points)
    }
  })

  // 计算当前绘制中的线段距离（仅在线段绘制模式下）
  let currentDistance = 0
  if (currentMode.value === 'line' && drawData.currentPoints.length >= 2) {
    currentDistance = calculatePolylineDistance(drawData.currentPoints)
  }

  // 计算总的显示距离
  const displayDistance = totalDistance + currentDistance

  // 判断是否显示距离面板
  const shouldShow =
    displayDistance > 0 || (currentMode.value === 'line' && drawData.currentPoints.length >= 2)

  if (!shouldShow) {
    distanceInfo.value.visible = false
    return
  }

  // 更新显示 - 合并为一句话
  const displayText = `长度: ${formatDistance(displayDistance)}`

  distanceInfo.value = {
    visible: true,
    displayText,
  }
}

// 智能坐标转换 - 只支持 WGS84 ↔ GCJ02 转换
const smartCoordinateTransform = (lng: number, lat: number, sourceCoordType?: string) => {
  console.log(`🔄 智能坐标转换 - 输入: [${lng}, ${lat}], 来源: ${sourceCoordType || '未知'}`)

  // 如果未指定坐标系类型，假设输入已经是正确的坐标系，不进行转换
  if (!sourceCoordType) {
    console.log(`🔄 未指定坐标系，保持原坐标: [${lng}, ${lat}]`)
    return { longitude: lng, latitude: lat }
  }

  let result = { longitude: lng, latitude: lat }

  // 根据平台和来源坐标系进行转换
  // #ifdef MP-WEIXIN
  // 微信小程序使用 GCJ02，如果输入是 WGS84 则需要转换
  if (sourceCoordType === 'WGS84') {
    const wgsPoint: CoordPoint = { latitude: lat, longitude: lng }
    const gcjPoint = wgs84ToGcj02(wgsPoint)
    result = { longitude: gcjPoint.longitude, latitude: gcjPoint.latitude }
    console.log(`🔄 微信小程序: WGS84 -> GCJ02: [${result.longitude}, ${result.latitude}]`)
  }
  // #endif

  // #ifdef APP-PLUS
  // App 端默认使用 WGS84，如果输入是其他坐标系则需要转换
  if (sourceCoordType === 'GCJ02') {
    const gcjPoint: CoordPoint = { latitude: lat, longitude: lng }
    const wgsPoint = gcj02ToWgs84(gcjPoint)
    result = { longitude: wgsPoint.longitude, latitude: wgsPoint.latitude }
    console.log(`🔄 App端: GCJ02 -> WGS84: [${result.longitude}, ${result.latitude}]`)
  }
  // #endif

  // #ifdef H5
  // H5 端通常使用 WGS84
  if (sourceCoordType === 'GCJ02') {
    const gcjPoint: CoordPoint = { latitude: lat, longitude: lng }
    const wgsPoint = gcj02ToWgs84(gcjPoint)
    result = { longitude: wgsPoint.longitude, latitude: wgsPoint.latitude }
    console.log(`🔄 H5端: GCJ02 -> WGS84: [${result.longitude}, ${result.latitude}]`)
  }
  // #endif

  return result
}

// 更新规范化单个点函数，优化坐标转换逻辑
const normalizePoint = (point: any, sourceCoordType?: string) => {
  if (!point) return null

  let lat, lng

  // 尝试不同的坐标字段名组合
  if (typeof point.latitude === 'number' && typeof point.longitude === 'number') {
    lat = point.latitude
    lng = point.longitude
  } else if (typeof point.lat === 'number' && typeof point.lng === 'number') {
    lat = point.lat
    lng = point.lng
  } else if (typeof point.lat === 'number' && typeof point.lon === 'number') {
    lat = point.lat
    lng = point.lon
  } else if (typeof point.y === 'number' && typeof point.x === 'number') {
    lat = point.y
    lng = point.x
  } else if (Array.isArray(point) && point.length >= 2) {
    // [lng, lat] 或 [lat, lng] 格式 - 通常 GeoJSON 是 [lng, lat]
    lng = parseFloat(point[0])
    lat = parseFloat(point[1])

    // 简单判断：如果第一个值大于90，可能是经度在前
    if (Math.abs(lng) > 90 && Math.abs(lat) <= 90) {
      // 很可能是 [lng, lat] 格式，保持不变
    } else if (Math.abs(lat) > 90 && Math.abs(lng) <= 90) {
      // 可能是 [lat, lng] 格式，需要交换
      ;[lat, lng] = [lng, lat]
    }
  } else {
    // 尝试字符串转换
    const latStr = point.latitude || point.lat || point.y
    const lngStr = point.longitude || point.lng || point.lon || point.x

    if (latStr !== undefined && lngStr !== undefined) {
      lat = parseFloat(latStr)
      lng = parseFloat(lngStr)
    }
  }

  // 验证坐标范围
  if (!isValidCoordinate(lat, lng)) {
    console.warn('⚠️ 无效坐标点:', point)
    return null
  }

  // 优化的坐标转换策略：
  // 1. 如果明确指定了sourceCoordType，且与目标坐标系不同，才进行转换
  // 2. 否则，假设输入坐标已经是正确的坐标系，不进行转换

  let finalLat = lat
  let finalLng = lng
  let transformMessage = '保持原坐标（未指定源坐标系或已匹配目标坐标系）'

  if (sourceCoordType) {
    // 确定目标坐标系（当前平台使用的坐标系）
    let targetCoordType = 'WGS84' // 默认

    // #ifdef MP-WEIXIN
    targetCoordType = 'GCJ02' // 微信小程序使用GCJ02
    // #endif

    // #ifdef APP-PLUS || H5
    targetCoordType = 'WGS84' // App和H5使用WGS84
    // #endif

    // 只有在源坐标系与目标坐标系不匹配时才转换
    if (sourceCoordType !== targetCoordType) {
      if (sourceCoordType === 'WGS84' && targetCoordType === 'GCJ02') {
        const transformed = wgs84ToGcj02({ latitude: lat, longitude: lng })
        finalLat = transformed.latitude
        finalLng = transformed.longitude
        transformMessage = `WGS84 → GCJ02 转换`
      } else if (sourceCoordType === 'GCJ02' && targetCoordType === 'WGS84') {
        const transformed = gcj02ToWgs84({ latitude: lat, longitude: lng })
        finalLat = transformed.latitude
        finalLng = transformed.longitude
        transformMessage = `GCJ02 → WGS84 转换`
      } else {
        transformMessage = `不支持的转换路径: ${sourceCoordType} → ${targetCoordType}`
      }
    } else {
      transformMessage = `坐标系匹配（${sourceCoordType}），无需转换`
    }
  }

  console.log('📍 坐标规范化:', {
    原始坐标: { lat, lng },
    源坐标系: sourceCoordType || '未指定',
    最终坐标: { lat: finalLat, lng: finalLng },
    处理说明: transformMessage,
  })

  return {
    id: point.id || `point_${Date.now()}_${Math.random()}`,
    latitude: finalLat,
    longitude: finalLng,
    iconPath: getIconPath('marker'), // 强制使用指定图标，确保回显时统一使用 fixedPoint-2.png
    width: point.width || 32,
    height: point.height || 38,
    originalCoords: { lat, lng }, // 保存原始坐标用于调试
    sourceCoordType, // 保存源坐标系信息
    transformMessage, // 保存转换说明
  }
}

// 地图移动事件处理 - 参考 map.vue 的简单实现
const handleRegionChange = (e: any) => {
  isMapMoving.value = e.type === 'begin'
}

// 获取默认图标路径
const getIconPath = (type: string) => {
  const iconPaths = {
    marker: '/static/fixedPoint-2.png',
    start_marker: '/static/fixedPoint-2.png', // 使用通用图标
    end_marker: '/static/fixedPoint-2.png', // 使用通用图标
    vertex_marker: '/static/fixedPoint-2.png', // 使用通用图标
    temp_marker: '/static/fixedPoint-2.png', // 使用通用图标
  }

  // 如果特定图标不存在，使用默认标记图标
  return iconPaths[type as keyof typeof iconPaths] || '/static/fixedPoint-2.png'
}

// 更新地图显示 - 改进版本，正确显示所有数据
const updateMapDisplay = () => {
  console.log('🗺️ 开始更新地图显示...')
  console.log('🗺️ 当前drawData:', {
    points: drawData.points,
    polylines: drawData.polylines,
    polygons: drawData.polygons,
    currentPoints: drawData.currentPoints,
  })

  // 清空现有显示数据
  mapMarkers.value = []
  mapPolygons.value = []
  mapPolylines.value = []

  // 显示已完成的点数据
  drawData.points.forEach((point: any, index: number) => {
    console.log(`📍 处理点${index + 1}:`, point)

    // 确保坐标有效
    if (!isValidCoordinate(point.latitude, point.longitude)) {
      console.warn(`⚠️ 跳过无效坐标点${index + 1}:`, point)
      return
    }

    const marker = {
      id: point.id || `point_${Date.now()}_${Math.random()}`,
      latitude: parseFloat(point.latitude),
      longitude: parseFloat(point.longitude),
      iconPath: point.iconPath || getIconPath('marker'),
      width: point.width || 32,
      height: point.height || 38,
      callout: {
        content: `点标记 ${index + 1}`,
        color: '#333',
        fontSize: 12,
        borderRadius: 4,
        bgColor: '#fff',
        padding: 5,
        display: 'BYCLICK',
      },
    }

    console.log(`✅ 添加地图标记${index + 1}:`, marker)
    mapMarkers.value.push(marker)
  })

  // 显示已完成的线数据
  drawData.polylines.forEach((polyline: any, index: number) => {
    console.log(`📏 处理线段${index + 1}:`, polyline)

    if (!polyline.points || polyline.points.length < 2) {
      console.warn(`⚠️ 跳过无效线段${index + 1}:`, polyline)
      return
    }

    // 验证线段的所有点
    const validPoints = polyline.points.filter((point: any) =>
      isValidCoordinate(point.latitude, point.longitude),
    )

    if (validPoints.length < 2) {
      console.warn(`⚠️ 线段${index + 1}有效点不足:`, {
        原始: polyline.points.length,
        有效: validPoints.length,
      })
      return
    }

    const lineData = {
      points: validPoints.map((point: any) => ({
        latitude: parseFloat(point.latitude),
        longitude: parseFloat(point.longitude),
      })),
      width: polyline.width || 4,
      color: polyline.color || '#2196F3',
      borderColor: polyline.borderColor || '#1976D2',
      borderWidth: polyline.borderWidth || 2,
    }

    console.log(`✅ 添加线段${index + 1}:`, lineData)
    mapPolylines.value.push(lineData)

    // 为线的端点添加标记
    if (validPoints.length > 0) {
      // 起点
      const startPoint = validPoints[0]
      mapMarkers.value.push({
        id: `line_start_${index}_${Date.now()}`,
        latitude: parseFloat(startPoint.latitude),
        longitude: parseFloat(startPoint.longitude),
        iconPath: getIconPath('start_marker'),
        width: 24,
        height: 29,
        callout: {
          content: `线${index + 1}起点`,
          color: '#333',
          fontSize: 11,
          borderRadius: 4,
          bgColor: '#E3F2FD',
          padding: 4,
          display: 'BYCLICK',
        },
      })

      // 终点
      if (validPoints.length > 1) {
        const endPoint = validPoints[validPoints.length - 1]
        mapMarkers.value.push({
          id: `line_end_${index}_${Date.now()}`,
          latitude: parseFloat(endPoint.latitude),
          longitude: parseFloat(endPoint.longitude),
          iconPath: getIconPath('end_marker'),
          width: 24,
          height: 29,
          callout: {
            content: `线${index + 1}终点`,
            color: '#333',
            fontSize: 11,
            borderRadius: 4,
            bgColor: '#E3F2FD',
            padding: 4,
            display: 'BYCLICK',
          },
        })
      }
    }
  })

  // 显示已完成的面数据
  drawData.polygons.forEach((polygon: any, index: number) => {
    console.log(`🔷 处理多边形${index + 1}:`, polygon)

    if (!polygon.points || polygon.points.length < 3) {
      console.warn(`⚠️ 跳过无效多边形${index + 1}:`, polygon)
      return
    }

    // 验证多边形的所有点
    const validPoints = polygon.points.filter((point: any) =>
      isValidCoordinate(point.latitude, point.longitude),
    )

    if (validPoints.length < 3) {
      console.warn(`⚠️ 多边形${index + 1}有效点不足:`, {
        原始: polygon.points.length,
        有效: validPoints.length,
      })
      return
    }

    const polygonData = {
      points: validPoints.map((point: any) => ({
        latitude: parseFloat(point.latitude),
        longitude: parseFloat(point.longitude),
      })),
      strokeWidth: polygon.strokeWidth || 3,
      strokeColor: polygon.strokeColor || '#67c23a', // 已完成状态颜色
      fillColor: polygon.fillColor || '#67c23a4d', // 已完成状态颜色的透明版本
    }

    console.log(`✅ 添加多边形${index + 1}:`, polygonData)
    mapPolygons.value.push(polygonData)

    // 为面的顶点添加标记
    validPoints.forEach((point: any, pointIndex: number) => {
      mapMarkers.value.push({
        id: `polygon_vertex_${index}_${pointIndex}_${Date.now()}`,
        latitude: parseFloat(point.latitude),
        longitude: parseFloat(point.longitude),
        iconPath: getIconPath('vertex_marker'),
        width: 20,
        height: 24,
        callout: {
          content: `面${index + 1}顶点${pointIndex + 1}`,
          color: '#333',
          fontSize: 10,
          borderRadius: 4,
          bgColor: '#E8F5E8',
          padding: 3,
          display: 'BYCLICK',
        },
      })
    })
  })

  // 在绘制模式下显示当前绘制的临时点
  if (currentMode.value === 'line' || currentMode.value === 'polygon') {
    console.log(`🚧 处理临时绘制点 (${currentMode.value}):`, drawData.currentPoints)

    drawData.currentPoints.forEach((point: any, index: number) => {
      if (isValidCoordinate(point.latitude, point.longitude)) {
        mapMarkers.value.push({
          id: `temp_${currentMode.value}_${index}_${Date.now()}`,
          latitude: parseFloat(point.latitude),
          longitude: parseFloat(point.longitude),
          iconPath: getIconPath('temp_marker'),
          width: 24,
          height: 29,
          callout: {
            content: `临时点${index + 1}`,
            color: '#333',
            fontSize: 11,
            borderRadius: 4,
            bgColor: '#FFF3E0',
            padding: 4,
            display: 'BYCLICK',
          },
        })
      }
    })

    // 显示正在绘制的图形预览
    if (currentMode.value === 'line' && drawData.currentPoints.length >= 2) {
      const validCurrentPoints = drawData.currentPoints.filter((point: any) =>
        isValidCoordinate(point.latitude, point.longitude),
      )

      if (validCurrentPoints.length >= 2) {
        mapPolylines.value.push({
          points: validCurrentPoints.map((point: any) => ({
            latitude: parseFloat(point.latitude),
            longitude: parseFloat(point.longitude),
          })),
          width: 3,
          color: '#FF9800',
          borderColor: '#F57C00',
          borderWidth: 1,
        })
      }
    } else if (currentMode.value === 'polygon' && drawData.currentPoints.length >= 3) {
      const validCurrentPoints = drawData.currentPoints.filter((point: any) =>
        isValidCoordinate(point.latitude, point.longitude),
      )

      if (validCurrentPoints.length >= 3) {
        mapPolygons.value.push({
          points: validCurrentPoints.map((point: any) => ({
            latitude: parseFloat(point.latitude),
            longitude: parseFloat(point.longitude),
          })),
          strokeWidth: 2,
          strokeColor: '#FF9800',
          fillColor: '#ff980033',
        })
      }
    }
  }

  const displayResult = {
    markers: mapMarkers.value.length,
    polylines: mapPolylines.value.length,
    polygons: mapPolygons.value.length,
  }

  console.log('🗺️ 地图显示更新完成:', displayResult)
  console.log('🗺️ 最终地图数据:', {
    mapMarkers: mapMarkers.value,
    mapPolylines: mapPolylines.value,
    mapPolygons: mapPolygons.value,
  })

  // 更新距离显示
  updateDistanceDisplay()
}

// 更新状态显示
const updateStatus = () => {
  const totalCount = drawData.points.length + drawData.polylines.length + drawData.polygons.length
  hasDrawnContent.value = totalCount > 0

  if (totalCount > 0) {
    const parts = []
    if (drawData.points.length > 0) {
      parts.push(`${drawData.points.length}个点`)
    }
    if (drawData.polylines.length > 0) {
      parts.push(`${drawData.polylines.length}条线`)
    }
    if (drawData.polygons.length > 0) {
      parts.push(`${drawData.polygons.length}个面`)
    }
    statusText.value = `已绘制: ${parts.join(', ')}`
  }
}

// 获取地图中心点 - 仅在用户点击确认时调用
const getMapCenter = async () => {
  try {
    const ctx = uni.createMapContext('mapDraw')
    return await new Promise<{ latitude: number; longitude: number }>((resolve, reject) => {
      ctx.getCenterLocation({
        success: resolve,
        fail: reject,
      })
    })
  } catch (err) {
    console.error('获取地图中心失败:', err)
    return null
  }
}

// 检查模式是否被允许
const isModeAllowed = (mode: string) => {
  const modeMap = {
    point: 'Point',
    line: 'LineString',
    polygon: 'Polygon',
  }
  return allowedModes.value.includes(modeMap[mode as keyof typeof modeMap])
}

// 设置绘制模式
const setMode = (mode: string) => {
  // 如果不允许多选且已有绘制内容，提示用户
  if (!allowMultiple.value && hasDrawnContent.value) {
    confirm({
      title: '替换现有内容',
      msg: '当前模式只允许绘制一个图形，是否要替换现有内容？',
      confirmButtonText: '替换',
      cancelButtonText: '取消',
    })
      .then(() => {
        // 清空现有内容后设置模式
        clearAllDrawing()
        setModeInternal(mode)
      })
      .catch(() => {
        // 用户取消，不做任何操作
      })
    return
  }

  // 如果有未完成的绘制，先完成
  if (currentMode.value && drawData.currentPoints.length > 0) {
    finishDrawing()
  }

  setModeInternal(mode)
}

// 内部设置模式函数
const setModeInternal = (mode: string) => {
  currentMode.value = mode
  showDrawControls.value = true
  drawData.currentPoints = []

  const modeNames = { point: '点', line: '线段', polygon: '多边形' }
  showNotify({
    type: 'primary',
    message: `开始绘制${modeNames[mode as keyof typeof modeNames]}`,
    duration: 2000,
    safeHeight: 88,
  })

  updateMapDisplay()
}

// 确认添加点
const confirmPoint = async () => {
  // 检查是否已有绘制内容，如果有则需要先确认是否替换
  if (hasDrawnContent.value) {
    try {
      await confirm({
        title: '替换现有内容',
        msg: '当前页面已有绘制内容，添加新内容将清空现有数据。是否继续？',
        confirmButtonText: '继续添加',
        cancelButtonText: '取消',
      })

      // 用户确认后，清空现有内容
      clearAllDrawing()
    } catch (error) {
      // 用户取消，直接返回不执行任何操作
      return
    }
  }

  const center = await getMapCenter()
  if (!center) return

  const point = {
    latitude: center.latitude,
    longitude: center.longitude,
  }

  if (currentMode.value === 'point') {
    // 在单选模式下，如果已有点标记，提示用户是否替换
    if (!allowMultiple.value && drawData.points.length > 0) {
      confirm({
        title: '替换现有点',
        msg: '当前模式只允许选择一个点，是否要替换现有的点标记？',
        confirmButtonText: '替换',
        cancelButtonText: '取消',
      })
        .then(() => {
          // 清空现有点后添加新点
          drawData.points = []
          addNewPoint(point)
        })
        .catch(() => {
          // 用户取消，不做任何操作
        })
      return
    }

    // 添加新点
    addNewPoint(point)
  } else if (currentMode.value === 'line' || currentMode.value === 'polygon') {
    // 添加到当前绘制
    drawData.currentPoints.push(point)

    const count = drawData.currentPoints.length
    const typeName = currentMode.value === 'line' ? '线段' : '多边形'

    showNotify({
      type: 'success',
      message: `${typeName}点已添加 (${count})`,
      duration: 1500,
      safeHeight: 88,
    })
    updateMapDisplay()
  }
}

// 添加新点的辅助函数
const addNewPoint = (point: { latitude: number; longitude: number }) => {
  drawData.points.push({
    id: Date.now() + drawData.points.length,
    latitude: point.latitude,
    longitude: point.longitude,
    iconPath: getIconPath('marker'),
    width: 32,
    height: 38,
  })

  showNotify({
    type: 'success',
    message: '点标记已添加',
    duration: 1500,
    safeHeight: 88,
  })
  updateStatus()
  updateMapDisplay()
}

// 取消上一个点
const cancelPoint = () => {
  if (currentMode.value === 'point') {
    // 删除最后一个用户点
    if (drawData.points.length > 0) {
      drawData.points.pop()
      showNotify({
        type: 'success',
        message: '已删除上一个点',
        duration: 1500,
        safeHeight: 88,
      })
      updateStatus()
      updateMapDisplay()
    }
  } else {
    // 删除当前绘制的最后一个点
    if (drawData.currentPoints.length > 0) {
      drawData.currentPoints.pop()
      showNotify({
        type: 'success',
        message: '已删除上一个顶点',
        duration: 1500,
        safeHeight: 88,
      })
      updateMapDisplay()
    }
  }
}

// 完成绘制
const finishDrawing = () => {
  if (currentMode.value === 'line') {
    if (drawData.currentPoints.length < 2) {
      showNotify({
        type: 'warning',
        message: '线段至少需要两个点',
        duration: 2000,
        safeHeight: 88,
      })
      return
    }

    drawData.polylines.push({
      points: [...drawData.currentPoints],
      width: 4,
      color: '#2196F3',
    })

    showNotify({
      type: 'success',
      message: '线段绘制完成',
      duration: 1500,
      safeHeight: 88,
    })
  } else if (currentMode.value === 'polygon') {
    if (drawData.currentPoints.length < 3) {
      showNotify({
        type: 'warning',
        message: '多边形至少需要三个顶点',
        duration: 2000,
        safeHeight: 88,
      })
      return
    }

    drawData.polygons.push({
      points: [...drawData.currentPoints],
      strokeWidth: 3,
      strokeColor: '#67c23a', // 已完成状态颜色
      fillColor: '#67c23a4d', // 已完成状态颜色的透明版本
    })

    showNotify({
      type: 'success',
      message: '多边形绘制完成',
      duration: 1500,
      safeHeight: 88,
    })
  }
  confirmDrawing()
  // 重置绘制状态
  // currentMode.value = ''
  // showDrawControls.value = false
  // drawData.currentPoints = []
  //
  // updateStatus()
  // updateMapDisplay()
}

// 清空所有绘制
const clearAllDrawing = () => {
  drawData = {
    points: [],
    polylines: [],
    polygons: [],
    currentPoints: [],
  }

  // currentMode.value = ''
  // showDrawControls.value = false

  updateStatus()
  updateMapDisplay()
  showNotify({
    type: 'success',
    message: '绘制内容已清空',
    duration: 1500,
    safeHeight: 88,
  })
}

// 清空确认
const clearAllWithConfirm = () => {
  confirm({
    title: '确认清空',
    msg: '确定要清空当前绘制内容吗？',
  })
    .then(() => {
      clearAllDrawing()
    })
    .catch(() => {
      // 用户取消，不做任何操作
    })
}

// 新增：将坐标转换为WGS84坐标系
const convertCoordinateToWGS84 = (lng: number, lat: number) => {
  console.log(`🔄 转换坐标为WGS84 - 输入: [${lng}, ${lat}]`)

  // 检测当前平台的坐标系
  let currentCoordType = 'WGS84' // 默认

  // #ifdef MP-WEIXIN
  currentCoordType = 'GCJ02' // 微信小程序使用GCJ02
  // #endif

  // #ifdef APP-PLUS || H5
  currentCoordType = 'WGS84' // App和H5使用WGS84
  // #endif

  let result = { longitude: lng, latitude: lat }

  // 如果当前是GCJ02坐标系，需要转换为WGS84
  if (currentCoordType === 'GCJ02') {
    const gcjPoint: CoordPoint = { latitude: lat, longitude: lng }
    const wgsPoint = gcj02ToWgs84(gcjPoint)
    result = { longitude: wgsPoint.longitude, latitude: wgsPoint.latitude }
    console.log(`🔄 ${currentCoordType} -> WGS84: [${result.longitude}, ${result.latitude}]`)
  } else {
    console.log(`🔄 已是WGS84坐标系，无需转换: [${result.longitude}, ${result.latitude}]`)
  }

  return result
}

// 新增：转换点数据为WKT格式
const convertPointsToWKT = (points: any[]) => {
  if (!points || points.length === 0) return []

  const wktResults: string[] = []

  points.forEach((point, index) => {
    if (isValidCoordinate(point.latitude, point.longitude)) {
      // 转换为WGS84
      const wgs84Point = convertCoordinateToWGS84(point.longitude, point.latitude)
      // 生成WKT格式 (注意WKT格式是 longitude latitude)
      const wkt = `POINT(${wgs84Point.longitude} ${wgs84Point.latitude})`
      wktResults.push(wkt)
      console.log(`📍 点${index + 1} WKT: ${wkt}`)
    }
  })

  return wktResults
}

// 新增：转换线数据为WKT格式
const convertLinesToWKT = (polylines: any[]) => {
  if (!polylines || polylines.length === 0) return []

  const wktResults: string[] = []

  polylines.forEach((line, index) => {
    if (line.points && line.points.length >= 2) {
      const coordinates: string[] = []

      line.points.forEach((point: any) => {
        if (isValidCoordinate(point.latitude, point.longitude)) {
          // 转换为WGS84
          const wgs84Point = convertCoordinateToWGS84(point.longitude, point.latitude)
          coordinates.push(`${wgs84Point.longitude} ${wgs84Point.latitude}`)
        }
      })

      if (coordinates.length >= 2) {
        const wkt = `LINESTRING(${coordinates.join(', ')})`
        wktResults.push(wkt)
        console.log(`📏 线${index + 1} WKT: ${wkt}`)
      }
    }
  })

  return wktResults
}

// 新增：转换面数据为WKT格式
const convertPolygonsToWKT = (polygons: any[]) => {
  if (!polygons || polygons.length === 0) return []

  const wktResults: string[] = []

  polygons.forEach((polygon, index) => {
    if (polygon.points && polygon.points.length >= 3) {
      const coordinates: string[] = []

      polygon.points.forEach((point: any) => {
        if (isValidCoordinate(point.latitude, point.longitude)) {
          // 转换为WGS84
          const wgs84Point = convertCoordinateToWGS84(point.longitude, point.latitude)
          coordinates.push(`${wgs84Point.longitude} ${wgs84Point.latitude}`)
        }
      })

      if (coordinates.length >= 3) {
        // 多边形需要闭合，确保第一个点和最后一个点相同
        const firstCoord = coordinates[0]
        const lastCoord = coordinates[coordinates.length - 1]
        if (firstCoord !== lastCoord) {
          coordinates.push(firstCoord)
        }

        const wkt = `POLYGON((${coordinates.join(', ')}))`
        wktResults.push(wkt)
        console.log(`🔷 面${index + 1} WKT: ${wkt}`)
      }
    }
  })

  return wktResults
}

// 新增：将所有几何数据转换为WKT格式
const convertGeometryToWKT = (data: any) => {
  console.log('🔄 开始转换几何数据为WKT格式:', data)

  const result = {
    points: [] as string[],
    lines: [] as string[],
    polygons: [] as string[],
    all: [] as string[],
    summary: '',
  }

  // 转换点数据
  if (data.points && data.points.length > 0) {
    result.points = convertPointsToWKT(data.points)
    result.all.push(...result.points)
  }

  // 转换线数据
  if (data.polylines && data.polylines.length > 0) {
    result.lines = convertLinesToWKT(data.polylines)
    result.all.push(...result.lines)
  }

  // 转换面数据
  if (data.polygons && data.polygons.length > 0) {
    result.polygons = convertPolygonsToWKT(data.polygons)
    result.all.push(...result.polygons)
  }

  // 生成摘要
  const summaryParts = []
  if (result.points.length > 0) {
    summaryParts.push(`${result.points.length}个点`)
  }
  if (result.lines.length > 0) {
    summaryParts.push(`${result.lines.length}条线`)
  }
  if (result.polygons.length > 0) {
    summaryParts.push(`${result.polygons.length}个面`)
  }
  result.summary = summaryParts.join(', ')

  console.log('✅ WKT转换完成:', result)
  return result
}

// 确认绘制
const confirmDrawing = () => {
  const totalCount = drawData.points.length + drawData.polylines.length + drawData.polygons.length

  if (totalCount === 0) {
    confirm({
      title: '没有绘制内容',
      msg: '您还没有绘制任何内容，是否直接返回？',
      confirmButtonText: '确定',
      cancelButtonText: '继续绘制',
    })
      .then(() => {
        // 返回空结果
        uni.$emit('mapDrawResult', {
          fieldName: fieldName.value,
          result: null,
        })
        uni.navigateBack()
      })
      .catch(() => {
        // 用户选择继续绘制，不做任何操作
      })
    return
  }

  // 转换所有几何数据为WGS84坐标的WKT格式
  const wktData = convertGeometryToWKT(drawData)

  // 生成geom字段的WKT字符串
  const geomString =
    wktData.all.length === 1
      ? wktData.all[0]
      : wktData.all.length > 1
        ? `GEOMETRYCOLLECTION(${wktData.all.join(', ')})`
        : ''

  console.log('🎯 生成的geom字符串:', geomString)

  // 发送结果到组件 - 直接返回geom字符串
  uni.$emit('mapDrawResult', {
    fieldName: fieldName.value,
    result: geomString,
  })

  // 显示成功提示
  showNotify({
    type: 'success',
    message: `已选择: ${wktData.summary}`,
    duration: 2000,
    safeHeight: 88,
  })

  // 返回上一页
  setTimeout(() => {
    uni.navigateBack()
  }, 1000)
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 操作按钮方法
const locateCurrentPosition = () => {
  uni.getLocation({
    type: 'gcj02',
    success: (res) => {
      mapLatitude.value = res.latitude
      mapLongitude.value = res.longitude
      showNotify({
        type: 'success',
        message: '定位成功',
        duration: 1500,
        safeHeight: 88,
      })
    },
    fail: () => {
      showNotify({
        type: 'danger',
        message: '定位失败',
        duration: 2000,
        safeHeight: 88,
      })
    },
  })
}

const saveDrawing = () => {
  confirmDrawing()
}

const undoLastAction = () => {
  if (currentMode.value && drawData.currentPoints.length > 0) {
    drawData.currentPoints.pop()
    updateMapDisplay()
    showNotify({
      type: 'success',
      message: '已撤销上一步',
      duration: 1500,
      safeHeight: 88,
    })
  } else if (hasDrawnContent.value) {
    // 撤销最后一个完成的图形
    if (drawData.polygons.length > 0) {
      drawData.polygons.pop()
    } else if (drawData.polylines.length > 0) {
      drawData.polylines.pop()
    } else if (drawData.points.length > 0) {
      drawData.points.pop()
    }
    updateStatus()
    updateMapDisplay()
    showNotify({
      type: 'success',
      message: '已撤销',
      duration: 1500,
      safeHeight: 88,
    })
  } else {
    showNotify({
      type: 'warning',
      message: '没有可撤销的操作',
      duration: 2000,
      safeHeight: 88,
    })
  }
}

// 计算几何图形的中心点
const calculateGeometryCenter = (data: any) => {
  let totalLat = 0
  let totalLng = 0
  let pointCount = 0

  // 收集所有点
  const allPoints: any[] = []

  // 添加点数据
  if (data.points && data.points.length > 0) {
    data.points.forEach((point: any) => {
      allPoints.push({ latitude: point.latitude, longitude: point.longitude })
    })
  }

  // 添加线数据的所有点
  if (data.polylines && data.polylines.length > 0) {
    data.polylines.forEach((line: any) => {
      if (line.points && line.points.length > 0) {
        line.points.forEach((point: any) => {
          allPoints.push({ latitude: point.latitude, longitude: point.longitude })
        })
      }
    })
  }

  // 添加面数据的所有点
  if (data.polygons && data.polygons.length > 0) {
    data.polygons.forEach((polygon: any) => {
      if (polygon.points && polygon.points.length > 0) {
        polygon.points.forEach((point: any) => {
          allPoints.push({ latitude: point.latitude, longitude: point.longitude })
        })
      }
    })
  }

  // 计算平均中心点
  if (allPoints.length > 0) {
    allPoints.forEach((point) => {
      totalLat += point.latitude
      totalLng += point.longitude
    })
    pointCount = allPoints.length

    return {
      latitude: totalLat / pointCount,
      longitude: totalLng / pointCount,
    }
  }

  return null
}

// 设置地图中心
const setMapCenter = (center: { latitude: number; longitude: number }) => {
  mapLatitude.value = center.latitude
  mapLongitude.value = center.longitude
}

// 新增：解析WKT字符串为地图数据格式
const parseWKTStringToMapData = (wktString) => {
  if (!wktString || typeof wktString !== 'string') return null

  const wktType = getWKTType(wktString)
  console.log('🔄 解析WKT字符串:', { wktString, wktType })

  try {
    switch (wktType) {
      case 'POINT': {
        const pointMatch = wktString.match(/POINT\s*\(\s*([\d.-]+)\s+([\d.-]+)\s*\)/i)
        if (pointMatch) {
          const lng = parseFloat(pointMatch[1])
          const lat = parseFloat(pointMatch[2])

          // 应用坐标转换
          const transformedCoords = smartCoordinateTransform(lng, lat, sourceCoordType.value)

          return {
            points: [
              {
                id: Date.now(), // 确保是数字类型
                latitude: transformedCoords.latitude,
                longitude: transformedCoords.longitude,
                iconPath: getIconPath('marker'),
                width: 32,
                height: 38,
              },
            ],
            polylines: [],
            polygons: [],
          }
        }
        break
      }

      case 'LINESTRING': {
        const coordMatches = wktString.match(/LINESTRING\s*\(\s*(.*?)\s*\)/i)
        if (coordMatches) {
          const coordPairs = coordMatches[1].split(',').map((pair) => pair.trim())
          const points = coordPairs
            .map((pair, index) => {
              const [lng, lat] = pair.split(/\s+/).map(parseFloat)
              if (!isNaN(lng) && !isNaN(lat)) {
                const transformedCoords = smartCoordinateTransform(lng, lat, sourceCoordType.value)
                return {
                  id: Date.now() + index, // 确保是数字类型
                  latitude: transformedCoords.latitude,
                  longitude: transformedCoords.longitude,
                }
              }
              return null
            })
            .filter((point) => point !== null)

          if (points.length >= 2) {
            return {
              points: [],
              polylines: [
                {
                  points,
                  width: 4,
                  color: '#2196F3',
                  borderColor: '#1976D2',
                  borderWidth: 2,
                },
              ],
              polygons: [],
            }
          }
        }
        break
      }

      case 'POLYGON': {
        const coordMatches = wktString.match(/POLYGON\s*\(\s*\((.*?)\)\s*\)/i)
        if (coordMatches) {
          const coordPairs = coordMatches[1].split(',').map((pair) => pair.trim())
          const points = coordPairs
            .map((pair, index) => {
              const [lng, lat] = pair.split(/\s+/).map(parseFloat)
              if (!isNaN(lng) && !isNaN(lat)) {
                const transformedCoords = smartCoordinateTransform(lng, lat, sourceCoordType.value)
                return {
                  id: Date.now() + index, // 确保是数字类型
                  latitude: transformedCoords.latitude,
                  longitude: transformedCoords.longitude,
                }
              }
              return null
            })
            .filter((point) => point !== null)

          if (points.length >= 3) {
            return {
              points: [],
              polylines: [],
              polygons: [
                {
                  points,
                  strokeWidth: 3,
                  strokeColor: '#67c23a', // 已完成状态颜色
                  fillColor: '#67c23a4d', // 已完成状态颜色的透明版本
                },
              ],
            }
          }
        }
        break
      }

      default:
        console.warn('🚫 暂不支持的WKT类型:', wktType)
        return null
    }
  } catch (error) {
    console.error('❌ WKT字符串解析出错:', error)
    return null
  }

  return null
}

// 获取WKT数据类型
const getWKTType = (wktString) => {
  if (!wktString || typeof wktString !== 'string') return null

  const upperWKT = wktString.trim().toUpperCase()

  if (upperWKT.startsWith('POINT')) return 'POINT'
  if (upperWKT.startsWith('MULTIPOINT')) return 'MULTIPOINT'
  if (upperWKT.startsWith('LINESTRING')) return 'LINESTRING'
  if (upperWKT.startsWith('MULTILINESTRING')) return 'MULTILINESTRING'
  if (upperWKT.startsWith('POLYGON')) return 'POLYGON'
  if (upperWKT.startsWith('MULTIPOLYGON')) return 'MULTIPOLYGON'
  if (upperWKT.startsWith('GEOMETRYCOLLECTION')) return 'GEOMETRYCOLLECTION'

  return null
}

// 获取地图初始坐标的函数
const getMapInitialCenter = async () => {
  try {
    console.log('🌍 开始获取地图初始坐标...')
    const response = await getValueByKey('urban.map.init.center')
    console.log('🌍 获取地图初始坐标响应:', response)

    if (response && response.data) {
      const coordString = response.data
      console.log('🌍 获取到坐标字符串:', coordString)

      // 解析坐标字符串，格式如 "129.5040, 42.9156"
      const coords = coordString.split(',').map((coord: string) => parseFloat(coord.trim()))

      if (coords.length === 2 && !isNaN(coords[0]) && !isNaN(coords[1])) {
        const [longitude, latitude] = coords
        console.log('🌍 解析得到坐标:', { longitude, latitude })

        // 设置地图初始坐标
        mapLongitude.value = longitude
        mapLatitude.value = latitude

        console.log('🌍 地图初始坐标设置成功:', {
          latitude: mapLatitude.value,
          longitude: mapLongitude.value,
        })

        return { latitude, longitude }
      } else {
        console.warn('⚠️ 坐标格式不正确:', coordString)
      }
    } else {
      console.warn('⚠️ 未获取到有效的坐标数据')
    }
  } catch (error) {
    console.error('❌ 获取地图初始坐标失败:', error)
  }

  return null
}

// 页面初始化 - 使用UniApp的onLoad生命周期
onLoad(async (options: Record<string, string>) => {
  console.log('🔧 页面参数:', options)

  // 设置页面参数
  if (options.title) {
    pageTitle.value = decodeURIComponent(options.title)
  }
  if (options.fieldName) {
    fieldName.value = decodeURIComponent(options.fieldName)
  }

  // 优先使用传入的坐标参数，如果没有则从接口获取
  let hasCustomCoords = false
  if (options.latitude) {
    mapLatitude.value = parseFloat(options.latitude)
    hasCustomCoords = true
  }
  if (options.longitude) {
    mapLongitude.value = parseFloat(options.longitude)
    hasCustomCoords = true
  }
  if (options.scale) {
    mapScale.value = parseInt(options.scale)
  }

  // 如果没有传入自定义坐标，则从接口获取初始坐标
  if (!hasCustomCoords) {
    await getMapInitialCenter()
  }

  // 解析允许的绘制模式
  if (options.allowedModes) {
    try {
      allowedModes.value = JSON.parse(decodeURIComponent(options.allowedModes))
      console.log('🔧 解析allowedModes参数:', allowedModes.value)
    } catch (e) {
      console.error('解析allowedModes失败:', e)
      allowedModes.value = ['Point', 'LineString', 'Polygon'] // 默认值
    }
  } else {
    // 如果没有传递allowedModes参数，使用默认值
    allowedModes.value = ['Point', 'LineString', 'Polygon']
    console.log('🔧 未传递allowedModes参数，使用默认值:', allowedModes.value)
  }

  // 确保至少设置第一个允许的模式为当前模式
  if (allowedModes.value.length > 0) {
    // 将内部模式名称转换为小写（Point -> point）
    const modeMap: Record<string, string> = {
      Point: 'point',
      LineString: 'line',
      Polygon: 'polygon',
    }
    const firstAllowedMode = allowedModes.value[0]
    currentMode.value = modeMap[firstAllowedMode] || 'point'
    showDrawControls.value = true
    drawData.currentPoints = []
    console.log('🔧 设置当前模式:', currentMode.value)
    console.log('🔧 显示绘制控制:', showDrawControls.value)
    updateMapDisplay()
  }

  // 解析是否允许多选
  if (options.allowMultiple !== undefined) {
    try {
      allowMultiple.value = options.allowMultiple === 'true'
      console.log('🔧 解析allowMultiple参数:', {
        原始值: options.allowMultiple,
        解析后: allowMultiple.value,
      })
    } catch (e) {
      console.error('解析allowMultiple失败:', e)
      allowMultiple.value = false // 默认只允许单选
    }
  } else {
    console.log('🔧 未传递allowMultiple参数，使用默认值:', allowMultiple.value)
  }

  // 解析坐标系类型参数
  if (options.coordType) {
    sourceCoordType.value = decodeURIComponent(options.coordType)
    console.log('🔧 解析坐标系类型参数:', sourceCoordType.value)
  } else {
    console.log('🔧 未传递坐标系类型参数，将进行智能识别')
  }

  // 如果有现有数据，加载它并居中显示
  if (options.existingData) {
    try {
      console.log('📍 原始existingData:', options.existingData)

      // 先尝试解码URL编码
      let decodedData = ''
      try {
        decodedData = decodeURIComponent(options.existingData)
        console.log('📍 URL解码后:', decodedData)
      } catch (decodeError) {
        console.warn('⚠️ URL解码失败，尝试直接使用原始数据:', decodeError)
        decodedData = options.existingData
      }

      // 检查解码后的数据是否像JSON
      if (!decodedData || typeof decodedData !== 'string') {
        throw new Error('解码后的数据为空或不是字符串')
      }

      // 如果数据以%开头，可能是双重编码，再次解码
      if (decodedData.startsWith('%')) {
        try {
          decodedData = decodeURIComponent(decodedData)
          console.log('📍 二次URL解码后:', decodedData)
        } catch (e) {
          console.warn('⚠️ 二次URL解码失败:', e)
        }
      }

      // 尝试解析JSON
      let existingData
      try {
        existingData = JSON.parse(decodedData)
        console.log('📍 JSON解析成功:', existingData)
      } catch (jsonError) {
        console.error('❌ JSON解析失败:', jsonError)
        console.log('📍 尝试解析的字符串前200字符:', decodedData.substring(0, 200))

        // 尝试修复常见的JSON问题
        let fixedData = decodedData

        // 移除可能的BOM字符
        if (fixedData.charCodeAt(0) === 0xfeff) {
          fixedData = fixedData.slice(1)
        }

        // 移除首尾空白字符
        fixedData = fixedData.trim()

        // 如果不是以{或[开头，可能不是JSON
        if (!fixedData.startsWith('{') && !fixedData.startsWith('[')) {
          throw new Error(
            `数据格式不正确，不是有效的JSON格式。数据开头: ${fixedData.substring(0, 50)}`,
          )
        }

        try {
          existingData = JSON.parse(fixedData)
          console.log('📍 修复后JSON解析成功:', existingData)
        } catch (fixJsonError) {
          throw new Error(`JSON解析最终失败: ${fixJsonError.message}`)
        }
      }

      if (existingData) {
        // 规范化数据格式，传递坐标系类型参数
        const normalizedData = normalizeExistingData(existingData, sourceCoordType.value)
        console.log('📍 规范化后的数据:', normalizedData)

        drawData.points = normalizedData.points || []
        drawData.polygons = normalizedData.polygons || []
        drawData.polylines = normalizedData.polylines || []

        console.log('📍 最终drawData:', {
          points: drawData.points,
          polygons: drawData.polygons,
          polylines: drawData.polylines,
        })

        // 计算并设置地图中心
        const center = calculateGeometryCenter(normalizedData)
        console.log('📍 计算的中心点:', center)
        if (center) {
          setMapCenter(center)
          console.log('🎯 地图已居中到现有数据:', center)
        }

        updateStatus()
        updateMapDisplay()
      } else {
        console.log('📍 existingData为空，跳过数据加载')
      }
    } catch (e) {
      console.error('❌ 处理现有数据时发生错误:', e)

      // 显示用户友好的错误提示
      alert({
        title: '数据加载失败',
        msg: `无法解析传入的地图数据：${e.message}\n\n将使用默认设置继续。`,
      })

      // 继续使用默认设置
      updateStatus()
      updateMapDisplay()
    }
  }
  // 处理WKT坐标字符串参数
  else if (options.coordinates) {
    try {
      console.log('📍 原始coordinates:', options.coordinates)

      // 解码WKT字符串
      const wktString = decodeURIComponent(options.coordinates)
      console.log('📍 WKT字符串解码后:', wktString)

      // 解析WKT字符串
      const wktData = parseWKTStringToMapData(wktString)
      console.log('📍 WKT解析结果:', wktData)

      if (wktData) {
        drawData.points = wktData.points || []
        drawData.polygons = wktData.polygons || []
        drawData.polylines = wktData.polylines || []

        console.log('📍 WKT数据加载到drawData:', {
          points: drawData.points,
          polygons: drawData.polygons,
          polylines: drawData.polylines,
        })

        // 计算并设置地图中心
        const center = calculateGeometryCenter(wktData)
        console.log('📍 WKT数据计算的中心点:', center)
        if (center) {
          setMapCenter(center)
          console.log('🎯 地图已居中到WKT数据:', center)
        }

        updateStatus()
        updateMapDisplay()
      } else {
        console.warn('⚠️ WKT数据解析失败')
        updateStatus()
        updateMapDisplay()
      }
    } catch (e) {
      console.error('❌ 处理WKT坐标数据时发生错误:', e)
      alert({
        title: '坐标数据加载失败',
        msg: `无法解析WKT坐标数据：${e.message}\n\n将使用默认设置继续。`,
      })
      updateStatus()
      updateMapDisplay()
    }
  } else {
    console.log('📍 未传递existingData或coordinates参数')
    updateStatus()
    updateMapDisplay()
  }
})

// 新增：规范化现有数据格式
const normalizeExistingData = (data: any, sourceCoordType?: string) => {
  console.log('🔄 开始规范化数据:', data)

  const normalized = {
    points: [] as any[],
    polygons: [] as any[],
    polylines: [] as any[],
  }

  // 处理不同的数据结构
  if (data) {
    // 如果是单个几何对象
    if (data.type || data.geometry) {
      const geometryPoints = normalizeGeometry(data)
      if (geometryPoints.length > 0) {
        normalized.points.push(...geometryPoints)
      }
    }
    // 如果是包含多个几何对象的结构
    else if (data.points || data.polygons || data.polylines) {
      if (data.points) {
        normalized.points = normalizePointsArray(data.points, sourceCoordType)
      }
      if (data.polygons) {
        normalized.polygons = normalizePolygonsArray(data.polygons, sourceCoordType)
      }
      if (data.polylines) {
        normalized.polylines = normalizePolylinesArray(data.polylines, sourceCoordType)
      }
    }
    // 如果是坐标数组
    else if (Array.isArray(data)) {
      normalized.points = normalizePointsArray(data, sourceCoordType)
    }
  }

  console.log('✅ 规范化完成:', normalized)
  return normalized
}

// 规范化几何对象
const normalizeGeometry = (geometry: any) => {
  const points: any[] = []

  if (geometry.type === 'Point' || geometry.geometry?.type === 'Point') {
    const coords = geometry.coordinates || geometry.geometry?.coordinates
    if (coords && Array.isArray(coords) && coords.length >= 2) {
      const normalizedPoint = normalizePoint(
        { longitude: coords[0], latitude: coords[1] },
        geometry.coordType,
      )
      if (normalizedPoint) {
        points.push(normalizedPoint)
      }
    }
  }

  return points
}

// 规范化点数组
const normalizePointsArray = (points: any[], sourceCoordType?: string) => {
  console.log('🔄 规范化点数组:', points)

  if (!Array.isArray(points)) {
    console.warn('⚠️ 传入的不是数组:', points)
    return []
  }

  return points
    .map((point, index) => {
      const normalized = normalizePoint(point, sourceCoordType)
      console.log(`📍 点${index + 1} 规范化:`, { 原始: point, 规范化: normalized })
      return normalized
    })
    .filter((point) => point !== null)
}

// 规范化多边形数组
const normalizePolygonsArray = (polygons: any[], sourceCoordType?: string) => {
  return polygons
    .map((polygon) => ({
      points: normalizePointsArray(polygon.points || [], sourceCoordType),
      strokeWidth: polygon.strokeWidth || 3,
      strokeColor: polygon.strokeColor || '#67c23a', // 已完成状态颜色
      fillColor: polygon.fillColor || '#67c23a4d', // 已完成状态颜色的透明版本
    }))
    .filter((polygon) => polygon.points.length >= 3)
}

// 规范化折线数组
const normalizePolylinesArray = (polylines: any[], sourceCoordType?: string) => {
  return polylines
    .map((polyline) => ({
      points: normalizePointsArray(polyline.points || [], sourceCoordType),
      width: polyline.width || 4,
      color: polyline.color || '#2196F3',
      borderColor: polyline.borderColor || '#1976D2',
      borderWidth: polyline.borderWidth || 2,
    }))
    .filter((polyline) => polyline.points.length >= 2)
}
</script>
