<template>
  <view class="map-draw-page">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-left">
        <wd-button size="small" type="text" @click="goBack">
          <wd-icon name="arrow-left" size="18px" />
          返回
        </wd-button>
      </view>
      <view class="header-title">{{ pageTitle }}</view>
      <view class="header-right">
        <wd-button size="small" type="primary" @click="confirmSelection">确定</wd-button>
      </view>
    </view>

    <!-- 地图容器 -->
    <view class="map-container">
      <map
        id="mapDraw"
        :key="mapKey"
        :enable-satellite="true"
        :latitude="mapLatitude"
        :longitude="mapLongitude"
        :scale="mapScale"
        :show-location="true"
        class="map-view"
        @regionchange="handleRegionChange"
      >
        <!-- 中心标记 -->
        <view class="center-marker"></view>

        <!-- 操作提示 -->
        <view class="operation-tip">
          <view class="tip-content">
            <wd-icon name="location" size="16px" />
            <text class="tip-text">移动地图选择位置</text>
          </view>
        </view>
      </map>
    </view>

    <!-- 坐标信息显示 -->
    <view class="coordinate-info">
      <view class="info-item">
        <text class="info-label">经度：</text>
        <text class="info-value">{{ currentCoordinates.longitude.toFixed(6) }}°</text>
      </view>
      <view class="info-item">
        <text class="info-label">纬度：</text>
        <text class="info-value">{{ currentCoordinates.latitude.toFixed(6) }}°</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { getValueByKey } from '@/service/userAPI'

// 页面参数
const pageTitle = ref('选择位置')
const fieldName = ref('mapSelector')

// 地图状态
const isMapMoving = ref(false)
// 地图配置
const mapLatitude = ref(43.823755)
const mapLongitude = ref(125.277062)
const mapScale = ref(16)
const mapKey = ref(0) // 添加key强制刷新地图

// 当前坐标信息
const currentCoordinates = reactive({
  latitude: 43.823755,
  longitude: 125.277062,
})

// 地图移动事件处理
const handleRegionChange = async (e: any) => {
  isMapMoving.value = e.type === 'begin'

  // 地图移动结束时更新坐标信息
  if (e.type === 'end') {
    try {
      const center = await getMapCenter()
      if (center) {
        currentCoordinates.latitude = center.latitude
        currentCoordinates.longitude = center.longitude
      }
    } catch (error) {
      console.error('获取地图中心失败:', error)
    }
  }
}

// 获取地图中心点
const getMapCenter = async () => {
  try {
    const ctx = uni.createMapContext('mapDraw')
    return await new Promise<{ latitude: number; longitude: number }>((resolve, reject) => {
      ctx.getCenterLocation({
        success: resolve,
        fail: reject,
      })
    })
  } catch (err) {
    console.error('获取地图中心失败:', err)
    return null
  }
}

// 确认选择
const confirmSelection = async () => {
  try {
    // 获取当前地图中心点
    const center = await getMapCenter()
    if (!center) {
      uni.showToast({ title: '获取位置失败', icon: 'error' })
      return
    }

    // 构建POINT格式的结果
    const pointResult = `POINT(${center.longitude} ${center.latitude})`

    // 发送结果到组件
    uni.$emit('mapDrawResult', {
      fieldName: fieldName.value,
      result: {
        type: 'point',
        coordinates: pointResult,
        latitude: center.latitude,
        longitude: center.longitude,
      },
    })

    console.log('📍 位置选择完成:', pointResult)

    uni.showToast({
      title: '位置选择完成',
      icon: 'success',
      duration: 1000,
    })

    // 延迟返回，让用户看到成功提示
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)
  } catch (error) {
    console.error('确认选择失败:', error)
    uni.showToast({ title: '选择失败', icon: 'error' })
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 强制更新地图
const forceUpdateMap = () => {
  mapKey.value++
  console.log('🔄 强制更新地图, key:', mapKey.value)
}

// 页面初始化
onMounted(async () => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  console.log('🚀 地图绘制页面初始化，接收参数:', options)

  // 设置页面参数
  if (options.title) {
    pageTitle.value = decodeURIComponent(options.title)
  }
  if (options.fieldName) {
    fieldName.value = decodeURIComponent(options.fieldName)
  }

  // 获取地图初始坐标的函数
  const getMapInitialCenter = async () => {
    try {
      console.log('🌍 开始获取地图初始坐标...')
      const response = await getValueByKey('urban.map.init.center')
      console.log('🌍 获取地图初始坐标响应:', response)

      if (response && response.data) {
        const coordString = response.data
        console.log('🌍 获取到坐标字符串:', coordString)

        // 解析坐标字符串，格式如 "129.5040, 42.9156"
        const coords = coordString.split(',').map((coord: string) => parseFloat(coord.trim()))

        if (coords.length === 2 && !isNaN(coords[0]) && !isNaN(coords[1])) {
          const [longitude, latitude] = coords
          console.log('🌍 解析得到坐标:', { longitude, latitude })

          // 设置地图初始坐标
          mapLongitude.value = longitude
          mapLatitude.value = latitude
          currentCoordinates.longitude = longitude
          currentCoordinates.latitude = latitude

          console.log('🌍 地图初始坐标设置成功:', {
            latitude: mapLatitude.value,
            longitude: mapLongitude.value,
          })

          return { latitude, longitude }
        } else {
          console.warn('⚠️ 坐标格式不正确:', coordString)
        }
      } else {
        console.warn('⚠️ 未获取到有效的坐标数据')
      }
    } catch (error) {
      console.error('❌ 获取地图初始坐标失败:', error)
    }

    return null
  }

  // 优先使用传递的经纬度参数
  let hasExistingPosition = false

  if (options.latitude) {
    const lat = parseFloat(options.latitude)
    if (!isNaN(lat)) {
      mapLatitude.value = lat
      currentCoordinates.latitude = lat
      hasExistingPosition = true
      console.log('📍 设置地图纬度:', lat)
    }
  }
  if (options.longitude) {
    const lng = parseFloat(options.longitude)
    if (!isNaN(lng)) {
      mapLongitude.value = lng
      currentCoordinates.longitude = lng
      hasExistingPosition = true
      console.log('📍 设置地图经度:', lng)
    }
  }
  if (options.scale) {
    mapScale.value = parseInt(options.scale)
  }

  // 处理直接传递的坐标数据（POINT格式字符串）
  if (options.coordinates) {
    try {
      const coordStr = decodeURIComponent(options.coordinates)
      console.log('🎯 接收到坐标字符串:', coordStr)
      console.log('🎯 坐标字符串类型:', typeof coordStr)

      // 解析 POINT(lng lat) 格式
      const pointMatch = coordStr.match(/POINT\s*\(\s*([\d.-]+)\s+([\d.-]+)\s*\)/i)
      if (pointMatch) {
        const lng = parseFloat(pointMatch[1])
        const lat = parseFloat(pointMatch[2])
        console.log('🔍 POINT格式解析结果:', {
          lng,
          lat,
          isValidLng: !isNaN(lng),
          isValidLat: !isNaN(lat),
        })

        if (!isNaN(lng) && !isNaN(lat)) {
          mapLatitude.value = lat
          mapLongitude.value = lng
          currentCoordinates.latitude = lat
          currentCoordinates.longitude = lng
          hasExistingPosition = true

          console.log('✅ POINT格式坐标解析成功:', { lng, lat })
          console.log('🗺️ 地图坐标已设置:', {
            mapLatitude: mapLatitude.value,
            mapLongitude: mapLongitude.value,
          })

          // 强制刷新地图
          setTimeout(() => {
            forceUpdateMap()
          }, 100)
        } else {
          console.warn('⚠️ POINT格式坐标解析失败: 无效的数值')
        }
      }
      // 处理逗号分隔格式 "lng,lat"
      else if (coordStr.includes(',')) {
        const parts = coordStr.split(',').map((s) => s.trim())
        console.log('🔍 逗号分隔格式解析:', parts)

        if (parts.length >= 2) {
          const lng = parseFloat(parts[0])
          const lat = parseFloat(parts[1])
          console.log('🔍 逗号分隔解析结果:', {
            lng,
            lat,
            isValidLng: !isNaN(lng),
            isValidLat: !isNaN(lat),
          })

          if (!isNaN(lng) && !isNaN(lat)) {
            mapLatitude.value = lat
            mapLongitude.value = lng
            currentCoordinates.latitude = lat
            currentCoordinates.longitude = lng
            hasExistingPosition = true

            console.log('✅ 逗号分隔坐标解析成功:', { lng, lat })

            // 强制刷新地图
            setTimeout(() => {
              forceUpdateMap()
            }, 100)
          } else {
            console.warn('⚠️ 逗号分隔坐标解析失败: 无效的数值')
          }
        } else {
          console.warn('⚠️ 逗号分隔格式错误: 分割后长度不足')
        }
      }
      // 尝试解析JSON格式
      else if (coordStr.startsWith('{') || coordStr.startsWith('[')) {
        console.log('🔍 尝试解析JSON格式:', coordStr)

        try {
          const parsed = JSON.parse(coordStr)
          console.log('🔍 JSON解析结果:', parsed)

          let lng, lat

          if (Array.isArray(parsed) && parsed.length >= 2) {
            ;[lng, lat] = parsed
            console.log('🔍 数组格式解析:', { lng, lat })
          } else if (typeof parsed === 'object' && parsed !== null) {
            lng = parsed.lng || parsed.longitude || parsed.x
            lat = parsed.lat || parsed.latitude || parsed.y
            console.log('🔍 对象格式解析:', { lng, lat, keys: Object.keys(parsed) })
          }

          console.log('🔍 最终坐标值:', {
            lng,
            lat,
            isValidLng: !isNaN(lng),
            isValidLat: !isNaN(lat),
          })

          if (!isNaN(lng) && !isNaN(lat)) {
            mapLatitude.value = lat
            mapLongitude.value = lng
            currentCoordinates.latitude = lat
            currentCoordinates.longitude = lng
            hasExistingPosition = true

            console.log('✅ JSON格式坐标解析成功:', { lng, lat })

            // 强制刷新地图
            setTimeout(() => {
              forceUpdateMap()
            }, 100)
          } else {
            console.warn('⚠️ JSON格式坐标解析失败: 无效的数值')
          }
        } catch (jsonError) {
          console.error('❌ JSON解析错误:', jsonError)
        }
      } else {
        console.warn('⚠️ 未识别的坐标格式:', coordStr)
      }
    } catch (e) {
      console.error('❌ 坐标数据解析失败:', e)
      console.error('❌ 错误详情:', {
        name: e.name,
        message: e.message,
        stack: e.stack,
      })
    }
  } else {
    console.log('ℹ️ 未接收到坐标参数')
  }

  // 如果有现有数据，解析并定位到该位置（兼容旧版本）
  if (options.existingData) {
    try {
      const existingData = JSON.parse(decodeURIComponent(options.existingData))
      if (existingData && existingData.points && existingData.points.length > 0) {
        const point = existingData.points[0]
        mapLatitude.value = point.latitude
        mapLongitude.value = point.longitude
        currentCoordinates.latitude = point.latitude
        currentCoordinates.longitude = point.longitude
        hasExistingPosition = true

        console.log('📍 加载现有位置 (兼容模式):', point)

        // 强制刷新地图
        setTimeout(() => {
          forceUpdateMap()
        }, 100)
      }
    } catch (e) {
      console.error('解析现有数据失败:', e)
    }
  }

  // 如果没有现有位置，则从接口获取初始坐标
  if (!hasExistingPosition) {
    await getMapInitialCenter()
  }

  // 显示回显成功提示
  if (hasExistingPosition) {
    console.log('✅ 位置回显成功:', {
      latitude: mapLatitude.value,
      longitude: mapLongitude.value,
    })

    setTimeout(() => {
      uni.showToast({
        title: '已定位到现有位置',
        icon: 'success',
        duration: 2000,
      })
    }, 300)
  }

  // 延迟获取初始中心点坐标（仅在没有现有位置时）
  setTimeout(async () => {
    try {
      const center = await getMapCenter()
      if (center) {
        currentCoordinates.latitude = center.latitude
        currentCoordinates.longitude = center.longitude

        if (!hasExistingPosition) {
          console.log('📍 获取到地图中心坐标:', center)
        }
      }
    } catch (error) {
      console.error('获取初始地图中心失败:', error)
    }
  }, 500)
})
</script>

<style lang="scss" scoped>
.map-draw-page {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #fff;
}

.page-header {
  z-index: 10;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.header-left,
.header-right {
  flex: 0 0 auto;
}

.header-title {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  text-align: center;
}

.map-container {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.map-view {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.center-marker {
  position: absolute;
  top: calc(50% - 20px);
  left: calc(50% - 20px);
  z-index: 999;
  width: 40px;
  height: 40px;
  pointer-events: none;
  background-image: url('@/static/fixedPoint.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

.operation-tip {
  position: absolute;
  top: 20px;
  left: 50%;
  z-index: 100;
  transform: translateX(-50%);
}

.tip-content {
  display: flex;
  gap: 6px;
  align-items: center;
  padding: 8px 16px;
  font-size: 12px;
  color: white;
  background: #000000b3;
  border-radius: 20px;
}

.tip-text {
  color: white;
}

.coordinate-info {
  position: absolute;
  right: 20px;
  bottom: 20px;
  left: 20px;
  z-index: 100;
  display: flex;
  gap: 20px;
  justify-content: center;
  padding: 12px 16px;
  background: #fffffff2;
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 2px 8px #0000001a;
}

.info-item {
  display: flex;
  gap: 4px;
  align-items: center;
}

.info-label {
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.info-value {
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

// 响应式适配
@media (max-width: 768px) {
  .coordinate-info {
    right: 10px;
    bottom: 10px;
    left: 10px;
    gap: 15px;
    padding: 10px 12px;
  }

  .operation-tip {
    top: 15px;
  }

  .tip-content {
    padding: 6px 12px;
    font-size: 11px;
  }
}
</style>
