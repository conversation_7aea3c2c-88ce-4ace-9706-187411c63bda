<!-- 工作台界面：白色主题设计 -->
<route lang="json5" type="home">
{
  layout: 'tabbar',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '',
  },
}
</route>

<template>
  <view class="workspace">
    <!-- 背景图片区域 -->
    <view class="background-section">
      <image
        class="background-image"
        mode="aspectFill"
        src="https://oss.urban.udo.top/urban/index-bg.svg"
        @error="onImageError"
        @load="onImageLoad"
      ></image>
      <!--      <image-->
      <!--        class="background-image"-->
      <!--        mode="aspectFill"-->
      <!--        src="../../static/loginbg.jpg"-->
      <!--        @error="onImageError"-->
      <!--        @load="onImageLoad"-->
      <!--      ></image>-->
      <view class="background-overlay"></view>
    </view>

    <!-- 内容区域 -->
    <view class="content-section">
      <!-- 固定顶部区域 -->
      <view class="header-content">
        <!-- 统计卡片 -->
        <view v-if="myTaskList.length > 0" class="stats-card">
          <view class="stats-header">
            <text class="stats-title">📊 任务统计</text>
            <!--            <text class="stats-icon">🌃</text>-->
          </view>
          <view class="stats-content">
            <view class="stat-item">
              <!--              <text class="stat-icon pending"></text>-->
              <image class="stat-icon" src="@/static/work/<EMAIL>" />
              <text class="stat-number primary">{{ getTaskCount(0) }}</text>
              <text class="stat-label">未开始</text>
            </view>
            <view class="stat-item">
              <image class="stat-icon" src="@/static/work/<EMAIL>" />

              <text class="stat-number success">{{ getTaskCount(1) }}</text>
              <text class="stat-label">进行中</text>
            </view>
            <view class="stat-item">
              <image class="stat-icon" src="@/static/work/<EMAIL>" />
              <text class="stat-number info">{{ getTaskCount(2) }}</text>
              <text class="stat-label">已完成</text>
            </view>
          </view>
        </view>
        <!-- 任务分类选择器 -->
        <view class="category-section">
          <view class="category-tabs">
            <view
              v-for="item in list"
              :key="item.value"
              :class="{ active: current === item.value, loading: isLoading }"
              class="category-tab"
              @tap="handleCategoryChange(item.value)"
            >
              <image
                v-if="item.label == '住房'"
                class="h-16 w-16"
                mode="scaleToFill"
                src="@/static/work/home.svg"
              />
              <image
                v-if="item.label == '小区'"
                class="h-16 w-16"
                mode="scaleToFill"
                src="@/static/work/villaArea.svg"
              />
              <image
                v-if="item.label == '社区'"
                class="h-16 w-16"
                mode="scaleToFill"
                src="@/static/work/community.svg"
              />
              <image
                v-if="item.label == '街区'"
                class="h-16 w-16"
                mode="scaleToFill"
                src="@/static/work/street.svg"
              />
              <view class="label">{{ item.label }}</view>
            </view>
          </view>
        </view>
      </view>
      <view class="task-list">
        <view class="task-section">
          <text class="task-section-title">我的任务</text>
          <text class="task-section-subtitle">（{{ myTaskList.length }}个任务）</text>
        </view>
      </view>

      <!-- 滚动任务列表区域 -->
      <scroll-view class="task-scroll-area" scroll-y>
        <!-- 加载状态 -->
        <view v-if="isLoading" class="loading-state">
          <view class="loading-spinner"></view>
          <text class="loading-text">🔄 加载中...</text>
        </view>

        <view v-else-if="myTaskList.length > 0" class="task-list">
          <!-- 任务卡片列表 -->
          <view
            v-for="item in myTaskList"
            :key="item.id"
            class="task-card"
            @tap="handleTaskTap(item)"
          >
            <view class="task-content">
              <view>
                <view class="task-header">
                  <view class="task-header-title">
                    {{ item.taskName }}
                  </view>
                  <view :class="getStatusClass(item.status)" class="task-status">
                    <text class="status-icon">{{ getStatusIcon(item.status) }}</text>
                    {{ getStatusText(item.status) }}
                  </view>
                </view>
                <view class="task-info">
                  <!--                  <view class="detail-text">-->
                  <!--                    <wd-icon name="spool" size="11px"></wd-icon>-->
                  <!--                    <text class="m-l-1 m-r-1">{{ item.taskId }}</text>-->
                  <!--                  </view>-->
                  <view class="detail-text">
                    <wd-icon name="user" size="11px"></wd-icon>
                    <text class="m-l-1 m-r-1">{{ item.leaderName }}</text>
                  </view>
                  <view class="detail-text">
                    <wd-icon name="usergroup" size="11px"></wd-icon>
                    <text class="m-l-1 m-r-1">{{ item.surveyorIds.length }}人</text>
                  </view>
                  <view class="detail-text">
                    <wd-icon name="layers" size="11px"></wd-icon>
                    <text class="m-l-1 m-r-1">
                      {{ item.completedSurveyObjects }}/{{ item.totalSurveyObjects }}
                    </text>
                  </view>
                </view>
              </view>
            </view>

            <view class="task-progress">
              <view class="progress-info">
                <!--                <view class="">完成度</view>-->
                <view class="">{{ item.completionRate }}%</view>
              </view>
              <wd-progress
                v-if="item.status === 0"
                :percentage="item.completionRate"
                color="var(--status-pending-color)"
                hide-text
              />
              <wd-progress
                v-if="item.status === 1"
                :percentage="item.completionRate"
                color="var(--status-active-color)"
                hide-text
              />
              <wd-progress
                v-if="item.status === 2"
                :percentage="item.completionRate"
                color="var(--status-completed-color)"
                hide-text
              />
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-else class="empty-state">
          <text class="empty-icon">🏙️</text>
          <text class="empty-title">暂无任务</text>
          <text class="empty-subtitle">当前分类下没有任务</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useDictStore, useTaskStore } from '@/store'
import { onLoad } from '@dcloudio/uni-app'
import { getMyTasks } from '@/service/task/taskAPI'

const dictStore = useDictStore()
const taskStore = useTaskStore()
const list = ref([])
const current = ref('')
const myTaskList = ref([])
const isLoading = ref(false)

/// 初始化数据
const getData = () => {
  list.value = dictStore.getDictByType('uc_task_type')
  console.log('🚀🚀🚀~~~当前数据，变量：list=====', list.value)
  current.value = list.value[0]?.value || ''
  getMyTask()
}

// 获取我的任务
const getMyTask = async () => {
  try {
    isLoading.value = true
    const res = await getMyTasks(current.value)
    console.log('🚀🚀🚀~~~任务数据，变量：res=====', res)
    myTaskList.value = res.list || []
  } catch (err) {
    console.log('🚀🚀🚀~~~错误信息，变量：err=====', err)
    myTaskList.value = []
  } finally {
    // 最少显示500ms加载状态，提供更好的用户体验
    setTimeout(() => {
      isLoading.value = false
    }, 500)
  }
}

// 分类切换处理
const handleCategoryChange = (value: string) => {
  if (value === current.value || isLoading.value) return
  current.value = value
  getMyTask()
}

// 分类切换处理（兼容旧方法）
const handleChange = () => {
  getMyTask()
}

// 任务点击处理
const handleTask = (taskItem: any) => {
  console.log('点击了任务==', taskItem)
  if (taskItem && taskItem.geom && typeof taskItem.geom === 'string') {
    // 设置完整的任务信息，包含geom和status
    taskStore.setCurrentTaskInfo({
      id: taskItem.id,
      geom: taskItem.geom,
      status: taskItem.status,
      title: taskItem.title,
      // 可以根据需要添加更多任务信息
    })
  } else {
    console.warn('⚠️ 传入的任务数据无效:', taskItem)
  }
}

// 获取任务数量统计
const getTaskCount = (status: number) => {
  return myTaskList.value.filter((item) => item.status === status).length
}

// 获取标签类型
const getTagType = (status: number) => {
  const types = ['default', 'primary', 'success']
  return types[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: number) => {
  const texts = ['未开始', '进行中', '已完成']
  return texts[status] || '未知'
}

// 获取状态样式类
const getStatusClass = (status: number) => {
  const classes = ['status-pending', 'status-active', 'status-completed']
  return classes[status] || 'status-pending'
}

// 获取状态图标
const getStatusIcon = (status: number) => {
  const icons = ['⏳', '⚡', '✅']
  return icons[status] || '⏳'
}

// 任务卡片点击
const handleTaskTap = (item: any) => {
  handleTask(item) // 传递完整的任务对象而不是只传递geom

  // 构建URL参数 - 兼容小程序环境
  const taskId = encodeURIComponent(item.id.toString())
  const taskName = encodeURIComponent(item.taskName)
  const taskType = encodeURIComponent(current.value)

  const url = `/pages/dataEntry/index?id=${taskId}&title=${taskName}&taskType=${taskType}`

  console.log('🚀 跳转到数据录入页面:', {
    taskId: item.id,
    taskName: item.taskName,
    taskType: current.value,
    url,
  })

  uni.navigateTo({
    url,
  })
}

// 任务操作
const handleTaskAction = (item: any, action: string) => {
  if (action === 'view') {
    handleTaskTap(item)
  } else if (action === 'start' || action === 'continue') {
    handleTaskTap(item)
  } else if (action === 'review') {
    handleTaskTap(item)
  }
}

// 图片加载处理
const onImageError = (e: any) => {
  console.warn('⚠️ 背景图片加载失败:', e)
}

const onImageLoad = (e: any) => {
  console.log('✅ 背景图片加载成功:', e)
}

onLoad(() => {
  getData()
})
</script>

<style lang="scss" scoped>
/* 白色主题设计变量 */
:root {
  /* 主色调 - 蓝色系 */
  --primary-color: #2563eb;
  --success-color: #16a34a;
  --warning-color: #ea580c;
  --info-color: #0891b2;
  --danger-color: #dc2626;
  /* 背景色 - 白色主题 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-glass: #ffffffe6;
  /* 文字色 - 深色文字 */
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  /* 边框色 */
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 #0000000d;
  --shadow-md: 0 4px 6px -1px #0000001a, 0 2px 4px -1px #0000000f;
  --shadow-lg: 0px 0px 10px 0px #0000004d;
  --shadow-xl: 0 20px 25px -5px #0000001a, 0 10px 10px -5px #0000000a;
  /* 圆角 - 减小弧度 */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 10px;
}
/* 主容器 */
.workspace {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background: var(--bg-secondary);
}
/* 背景图片区域 */
.background-section {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 220px;
  overflow: hidden;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  //border-radius: 0 0 24px 24px;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #0000004d 0%, #0000001a 100%);
  border-radius: 0 0 24px 24px;
}
/* 内容区域 */
.content-section {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
}
/* 固定顶部区域 */
.header-content {
  position: relative;
  padding: 210px 12px 10px;
  background: transparent;
}
/* 分类选择器 */
.category-section {
  margin-top: 10px;
  .category-tabs {
    display: flex;
    padding: 4px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    .active {
      color: #ffffff !important;
      background: #1d4ed8b3;
    }
    .category-tab {
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 8px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--text-secondary);
      border-radius: var(--radius-md);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      .label {
        margin-top: -20px;
      }
    }
  }
}
/* 统计卡片 */
.stats-card {
  padding: 16px;
  margin-bottom: 0;
  background: #ffffff;
  //border: 1px solid var(--border-color);
  border-radius: 10px;
  box-shadow: var(--shadow-lg);

  .stats-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .stats-title {
      font-size: 16px;
      font-weight: 700;
      color: var(--text-primary);
    }

    .stats-icon {
      font-size: 18px;
    }
  }

  .stats-content {
    display: flex;
    justify-content: space-around;

    .stat-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      text-align: center;
      .stat-icon {
        display: block;
        width: 60rpx;
        height: 60rpx;
        margin-right: 4px;

        &.pending {
          color: var(--status-pending-color);
        }
        &.active {
          color: var(--status-active-color);
        }
        &.completed {
          color: var(--status-completed-color);
        }
      }

      .stat-number {
        display: block;
        margin-right: 3px;
        font-size: 22px;
        font-weight: 800;

        &.primary {
          color: var(--status-pending-color);
        }
        &.success {
          color: var(--status-active-color);
        }
        &.info {
          color: var(--status-completed-color);
        }
      }

      .stat-label {
        font-size: 12px;
        font-weight: 500;
        color: var(--text-tertiary);
      }
    }
  }
}
/* 滚动任务列表区域 */
.task-scroll-area {
  flex: 1;
  width: 100%;
  background: var(--bg-secondary);
}
/* 任务列表 */
.task-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0 12px 10px;
}
/* 任务卡片 */
.task-card {
  position: relative;
  padding: 14px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  //transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  .task-content {
    //display: flex;
    //align-items: center;
    //justify-content: space-between;
  }
  .task-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 10px;
    &-title {
      max-width: 240px;
      margin-right: 10px;
      overflow: hidden; /* 隐藏溢出内容 */
      font-size: 14px;
      font-weight: 700;
      text-overflow: ellipsis; /* 添加省略号 */
      white-space: nowrap; /* 禁止换行 */
    }
    .task-status {
      display: flex;
      align-items: center;
      padding: 2px 5px;
      font-size: 11px;
      font-weight: 600;
      white-space: nowrap;
      border-radius: var(--radius-lg);
      .status-icon {
        font-size: 12px;
      }

      &.status-pending {
        color: var(--status-pending-color);
        background: var(--status-pending-bg);
        border: 1px solid var(--status-pending-border);
      }

      &.status-active {
        color: var(--status-active-color);
        background: var(--status-active-bg);
        border: 1px solid var(--status-active-border);
      }

      &.status-completed {
        color: var(--status-completed-color);
        background: var(--status-completed-bg);
        border: 1px solid var(--status-completed-border);
      }
    }
  }
  .task-info {
    display: flex;
    .detail-text {
      margin-right: 4px;
      font-size: 11px;
      color: var(--text-secondary);
    }
  }
  .task-details {
    //padding: 8px 10px;
    margin-bottom: 12px;
    //background: var(--bg-tertiary);
    //border-left: 3px solid var(--primary-color);
    //border-radius: var(--radius-md);
  }

  .task-actions {
    .action-btn {
      padding: 10px 8px;
      font-size: 13px;
      font-weight: 600;
      border-radius: var(--radius-md);
      .btn-icon {
        font-size: 14px;
      }

      &.primary {
        color: white;
        background: var(--primary-color);
        box-shadow: var(--shadow-sm);

        &:active {
          background: #1d4ed8;
          transform: translateY(1px);
        }
      }

      &.secondary {
        color: #92400e;
        background: #fef3c7;
      }

      &.success {
        color: white;
        background: var(--success-color);
        box-shadow: var(--shadow-sm);
      }
    }
  }
}
/* 空状态 */
.empty-state {
  padding: 40px 20px;
  text-align: center;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);

  .empty-icon {
    display: block;
    margin-bottom: 12px;
    font-size: 40px;
    opacity: 0.6;
  }

  .empty-title {
    display: block;
    margin-bottom: 4px;
    font-size: 16px;
    font-weight: 700;
    color: var(--text-primary);
  }

  .empty-subtitle {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-tertiary);
  }
}
/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);

  .loading-spinner {
    width: 28px;
    height: 28px;
    margin-bottom: 12px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
  }
}
/* 动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* 响应式 */
@media (max-width: 375px) {
  .main-content {
    padding: 140px 8px 12px;
  }

  .task-card {
    padding: 12px;
  }

  .stats-card {
    padding: 14px;
  }
}
.task-section {
  padding-left: 10px;
  border-left: var(--primary-color) 3px solid;
  &-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
  }
}
.task-progress {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  margin: 8px 0;
  .progress-info {
    display: flex;
    align-items: end;
    justify-content: space-between;
    padding-right: 10px;
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
  }
}
</style>
